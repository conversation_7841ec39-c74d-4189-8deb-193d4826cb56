import { z } from "zod";

/**
 * Schema para CEP
 */
export const cepSchema = z
  .string()
  .optional()
  .refine((cep) => {
    if (!cep || cep.trim() === "") return true; // Campo opcional

    // Remove caracteres não numéricos
    const cleanCep = cep.replace(/\D/g, "");

    // Verifica se tem 8 dígitos
    return cleanCep.length === 8;
  }, "CEP deve ter 8 dígitos");

/**
 * Schema para cidade
 */
export const citySchema = z
  .string()
  .optional()
  .refine((city) => {
    if (!city || city.trim() === "") return true; // Campo opcional
    return city.length >= 2 && city.length <= 100;
  }, "Cidade deve ter entre 2 e 100 caracteres");

/**
 * Schema para estado
 */
export const stateSchema = z
  .string()
  .optional()
  .refine((state) => {
    if (!state || state.trim() === "") return true; // Campo opcional

    // Lista de estados válidos do Brasil
    const validStates = [
      "AC",
      "AL",
      "AP",
      "AM",
      "BA",
      "CE",
      "DF",
      "ES",
      "GO",
      "MA",
      "MT",
      "MS",
      "MG",
      "PA",
      "PB",
      "PR",
      "PE",
      "PI",
      "RJ",
      "RN",
      "RS",
      "RO",
      "RR",
      "SC",
      "SP",
      "SE",
      "TO",
    ];

    return validStates.includes(state.toUpperCase());
  }, "Estado inválido");

/**
 * Schema para logradouro
 */
export const streetSchema = z
  .string()
  .optional()
  .refine((street) => {
    if (!street || street.trim() === "") return true; // Campo opcional
    return street.length >= 5 && street.length <= 200;
  }, "Logradouro deve ter entre 5 e 200 caracteres");

/**
 * Schema para bairro
 */
export const neighborhoodSchema = z
  .string()
  .optional()
  .refine((neighborhood) => {
    if (!neighborhood || neighborhood.trim() === "") return true; // Campo opcional
    return neighborhood.length >= 2 && neighborhood.length <= 100;
  }, "Bairro deve ter entre 2 e 100 caracteres");

/**
 * Schema para número
 */
export const numberSchema = z
  .string()
  .optional()
  .refine((number) => {
    if (!number || number.trim() === "") return true; // Campo opcional
    return number.length >= 1 && number.length <= 10;
  }, "Número deve ter entre 1 e 10 caracteres");

/**
 * Schema para complemento
 */
export const complementSchema = z
  .string()
  .optional()
  .refine((complement) => {
    if (!complement || complement.trim() === "") return true; // Campo opcional
    return complement.length <= 100;
  }, "Complemento deve ter no máximo 100 caracteres");

/**
 * Schema completo para endereço
 */
export const addressSchema = z.object({
  cep: cepSchema,
  cidade: citySchema,
  estado: stateSchema,
  logradouro: streetSchema,
  bairro: neighborhoodSchema,
  numero: numberSchema,
  complemento: complementSchema,
});

/**
 * Schema para endereço obrigatório (quando necessário)
 */
export const requiredAddressSchema = z.object({
  cep: z
    .string()
    .min(1, "CEP é obrigatório")
    .refine((cep) => {
      const cleanCep = cep.replace(/\D/g, "");
      return cleanCep.length === 8;
    }, "CEP deve ter 8 dígitos"),

  cidade: z
    .string()
    .min(1, "Cidade é obrigatória")
    .min(2, "Cidade deve ter pelo menos 2 caracteres"),

  estado: z
    .string()
    .min(1, "Estado é obrigatório")
    .refine((state) => {
      const validStates = [
        "AC",
        "AL",
        "AP",
        "AM",
        "BA",
        "CE",
        "DF",
        "ES",
        "GO",
        "MA",
        "MT",
        "MS",
        "MG",
        "PA",
        "PB",
        "PR",
        "PE",
        "PI",
        "RJ",
        "RN",
        "RS",
        "RO",
        "RR",
        "SC",
        "SP",
        "SE",
        "TO",
      ];
      return validStates.includes(state.toUpperCase());
    }, "Estado inválido"),

  logradouro: z
    .string()
    .min(1, "Logradouro é obrigatório")
    .min(5, "Logradouro deve ter pelo menos 5 caracteres"),

  bairro: z
    .string()
    .min(1, "Bairro é obrigatório")
    .min(2, "Bairro deve ter pelo menos 2 caracteres"),

  numero: z.string().min(1, "Número é obrigatório"),

  complemento: complementSchema,
});

// Types derivados dos schemas
export type Address = z.infer<typeof addressSchema>;
export type RequiredAddress = z.infer<typeof requiredAddressSchema>;
