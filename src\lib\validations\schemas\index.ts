// Schema exports
export * from "./auth";
export * from "./user";
export * from "./address";
export * from "./registration";

import { z } from "zod";
import { personalDataSchema } from "./user";
import { addressSchema } from "./address";
import { userDataBaseSchema } from "./auth";

/**
 * Schema completo para cadastro de usuário
 * Combina dados pessoais, endereço e dados de usuário
 */
export const fullRegistrationSchema = personalDataSchema
  .merge(addressSchema)
  .merge(userDataBaseSchema)
  .refine((data) => data.senha === data.confirmarSenha, {
    message: "Senhas não coincidem",
    path: ["confirmarSenha"],
  });

/**
 * Schema para validação de campo único
 * Útil para validação em tempo real
 */
export const singleFieldSchema = z.object({
  fieldName: z.string(),
  value: z.unknown(),
});

// Types derivados
export type FullRegistrationData = z.infer<typeof fullRegistrationSchema>;
export type SingleFieldData = z.infer<typeof singleFieldSchema>;

// Utility type para extrair erros de validação do Zod
export type ValidationErrors<T> = Partial<Record<keyof T, string>>;

// Helper para converter erros do Zod para formato de objeto
export function formatZodErrors<T>(error: z.ZodError<T>): ValidationErrors<T> {
  const errors: ValidationErrors<T> = {};

  error.errors.forEach((err) => {
    const path = err.path.join(".") as keyof T;
    errors[path] = err.message;
  });

  return errors;
}
