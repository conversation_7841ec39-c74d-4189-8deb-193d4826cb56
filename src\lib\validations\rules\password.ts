/**
 * Validação e utilitários para senhas
 */

/**
 * Critérios de força da senha
 */
export type PasswordStrength = 'weak' | 'fair' | 'good' | 'strong';

/**
 * Resultado da análise de força da senha
 */
export type PasswordAnalysis = {
  strength: PasswordStrength;
  score: number; // 0-100
  feedback: string[];
  isValid: boolean;
};

/**
 * Configuração para validação de senha
 */
export type PasswordConfig = {
  minLength?: number;
  maxLength?: number;
  requireUppercase?: boolean;
  requireLowercase?: boolean;
  requireNumbers?: boolean;
  requireSpecialChars?: boolean;
  forbiddenPatterns?: string[];
  forbiddenWords?: string[];
};

/**
 * Configuração padrão para senhas
 */
const DEFAULT_PASSWORD_CONFIG: PasswordConfig = {
  minLength: 6,
  maxLength: 100,
  requireUppercase: false,
  requireLowercase: false,
  requireNumbers: false,
  requireSpecialChars: false,
  forbiddenPatterns: [],
  forbiddenWords: ['password', 'senha', '123456', 'qwerty'],
};

/**
 * Valida senha básica (apenas comprimento)
 */
export function validatePassword(password: string, minLength = 6): boolean {
  if (!password || typeof password !== 'string') return false;
  return password.length >= minLength;
}

/**
 * Valida senha com configuração avançada
 */
export function validatePasswordAdvanced(
  password: string,
  config: PasswordConfig = DEFAULT_PASSWORD_CONFIG
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  const finalConfig = { ...DEFAULT_PASSWORD_CONFIG, ...config };

  if (!password || typeof password !== 'string') {
    errors.push('Senha é obrigatória');
    return { isValid: false, errors };
  }

  // Comprimento mínimo
  if (finalConfig.minLength && password.length < finalConfig.minLength) {
    errors.push(`Senha deve ter pelo menos ${finalConfig.minLength} caracteres`);
  }

  // Comprimento máximo
  if (finalConfig.maxLength && password.length > finalConfig.maxLength) {
    errors.push(`Senha deve ter no máximo ${finalConfig.maxLength} caracteres`);
  }

  // Letra maiúscula
  if (finalConfig.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Senha deve conter pelo menos uma letra maiúscula');
  }

  // Letra minúscula
  if (finalConfig.requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Senha deve conter pelo menos uma letra minúscula');
  }

  // Números
  if (finalConfig.requireNumbers && !/\d/.test(password)) {
    errors.push('Senha deve conter pelo menos um número');
  }

  // Caracteres especiais
  if (finalConfig.requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Senha deve conter pelo menos um caractere especial');
  }

  // Padrões proibidos
  if (finalConfig.forbiddenPatterns) {
    for (const pattern of finalConfig.forbiddenPatterns) {
      if (new RegExp(pattern, 'i').test(password)) {
        errors.push('Senha contém padrão não permitido');
        break;
      }
    }
  }

  // Palavras proibidas
  if (finalConfig.forbiddenWords) {
    const lowerPassword = password.toLowerCase();
    for (const word of finalConfig.forbiddenWords) {
      if (lowerPassword.includes(word.toLowerCase())) {
        errors.push('Senha contém palavra não permitida');
        break;
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Analisa a força da senha
 */
export function analyzePasswordStrength(password: string): PasswordAnalysis {
  if (!password) {
    return {
      strength: 'weak',
      score: 0,
      feedback: ['Senha é obrigatória'],
      isValid: false,
    };
  }

  let score = 0;
  const feedback: string[] = [];

  // Comprimento (0-25 pontos)
  if (password.length >= 8) {
    score += 25;
  } else if (password.length >= 6) {
    score += 15;
    feedback.push('Use pelo menos 8 caracteres para maior segurança');
  } else {
    score += 5;
    feedback.push('Senha muito curta');
  }

  // Letras minúsculas (0-10 pontos)
  if (/[a-z]/.test(password)) {
    score += 10;
  } else {
    feedback.push('Adicione letras minúsculas');
  }

  // Letras maiúsculas (0-10 pontos)
  if (/[A-Z]/.test(password)) {
    score += 10;
  } else {
    feedback.push('Adicione letras maiúsculas');
  }

  // Números (0-10 pontos)
  if (/\d/.test(password)) {
    score += 10;
  } else {
    feedback.push('Adicione números');
  }

  // Caracteres especiais (0-15 pontos)
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    score += 15;
  } else {
    feedback.push('Adicione caracteres especiais (!@#$%^&*)');
  }

  // Variedade de caracteres (0-10 pontos)
  const uniqueChars = new Set(password).size;
  if (uniqueChars >= password.length * 0.7) {
    score += 10;
  } else if (uniqueChars >= password.length * 0.5) {
    score += 5;
  } else {
    feedback.push('Use mais variedade de caracteres');
  }

  // Padrões comuns (penalização)
  if (/(.)\1{2,}/.test(password)) {
    score -= 10;
    feedback.push('Evite repetir caracteres consecutivos');
  }

  if (/123|abc|qwe/i.test(password)) {
    score -= 15;
    feedback.push('Evite sequências óbvias');
  }

  // Determina força
  let strength: PasswordStrength;
  if (score >= 80) {
    strength = 'strong';
  } else if (score >= 60) {
    strength = 'good';
  } else if (score >= 40) {
    strength = 'fair';
  } else {
    strength = 'weak';
  }

  // Feedback positivo para senhas fortes
  if (strength === 'strong' && feedback.length === 0) {
    feedback.push('Senha forte!');
  }

  return {
    strength,
    score: Math.max(0, Math.min(100, score)),
    feedback,
    isValid: score >= 40, // Considera válida se for pelo menos 'fair'
  };
}

/**
 * Gera uma senha aleatória
 */
export function generatePassword(
  length = 12,
  includeUppercase = true,
  includeLowercase = true,
  includeNumbers = true,
  includeSpecialChars = true
): string {
  let charset = '';
  
  if (includeLowercase) charset += 'abcdefghijklmnopqrstuvwxyz';
  if (includeUppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  if (includeNumbers) charset += '0123456789';
  if (includeSpecialChars) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';

  if (!charset) {
    throw new Error('Pelo menos um tipo de caractere deve ser incluído');
  }

  let password = '';
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }

  return password;
}
