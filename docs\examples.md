# Exemplos Práticos - Sistema de Validação

Este documento contém exemplos práticos de uso do sistema de validação para diferentes cenários.

## 1. Formulário de Login Simples

```typescript
import { useForm } from '@/hooks/form/useForm';
import { loginUserSchema } from '@/lib/validations/schemas/auth';
import { FormField, Button } from '@/components/forms';

export function LoginForm() {
  const {
    formData,
    errors,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
  } = useForm({
    initialValues: {
      email: "",
      senha: ""
    },
    validation: {
      schema: loginUserSchema
    },
    callbacks: {
      onSubmit: async (values) => {
        try {
          const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(values)
          });
          
          if (response.ok) {
            window.location.href = '/dashboard';
          } else {
            throw new Error('Credenciais inválidas');
          }
        } catch (error) {
          alert('Erro no login: ' + error.message);
        }
      }
    }
  });

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-center">Login</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <FormField
          label="Email"
          name="email"
          type="email"
          value={formData.email || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.email}
          required
          placeholder="<EMAIL>"
        />

        <FormField
          label="Senha"
          name="senha"
          type="password"
          value={formData.senha || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.senha}
          required
          placeholder="Sua senha"
        />

        <Button
          type="submit"
          className="w-full"
          isLoading={isSubmitting}
          disabled={isSubmitting}
        >
          {isSubmitting ? "Entrando..." : "Entrar"}
        </Button>
      </form>
    </div>
  );
}
```

## 2. Formulário de Cadastro de Pessoa Física

```typescript
import { useForm } from '@/hooks/form/useForm';
import { personalDataSchema } from '@/lib/validations/schemas/user';
import { FormField, FormSelect, MaskedFormField } from '@/components/forms';

const generos = [
  { value: "masculino", label: "Masculino" },
  { value: "feminino", label: "Feminino" },
  { value: "outro", label: "Outro" },
  { value: "prefiro_nao_informar", label: "Prefiro não informar" }
];

export function PersonalDataForm() {
  const {
    formData,
    errors,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
  } = useForm({
    initialValues: {
      tipoPessoa: "fisica",
      email: "",
      nomeCompleto: "",
      dataNascimento: "",
      genero: "",
      cpf: "",
      rg: "",
      telefone: "",
      celular: ""
    },
    validation: {
      schema: personalDataSchema
    },
    callbacks: {
      onSubmit: async (values) => {
        console.log("Dados pessoais:", values);
        // Processar dados
      }
    }
  });

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">Dados Pessoais</h2>
      
      <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          label="Email"
          name="email"
          type="email"
          value={formData.email || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.email}
          required
          className="md:col-span-2"
        />

        <FormField
          label="Nome Completo"
          name="nomeCompleto"
          value={formData.nomeCompleto || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.nomeCompleto}
          required
          className="md:col-span-2"
        />

        <FormField
          label="Data de Nascimento"
          name="dataNascimento"
          type="date"
          value={formData.dataNascimento || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.dataNascimento}
        />

        <FormSelect
          label="Gênero"
          name="genero"
          value={formData.genero || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          options={generos}
          error={errors.genero}
        />

        <MaskedFormField
          label="CPF"
          name="cpf"
          value={formData.cpf || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          mask="cpf"
          error={errors.cpf}
        />

        <FormField
          label="RG"
          name="rg"
          value={formData.rg || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.rg}
        />

        <MaskedFormField
          label="Telefone"
          name="telefone"
          value={formData.telefone || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          mask="phone"
          error={errors.telefone}
        />

        <MaskedFormField
          label="Celular"
          name="celular"
          value={formData.celular || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          mask="phone"
          error={errors.celular}
        />

        <Button
          type="submit"
          className="md:col-span-2"
          isLoading={isSubmitting}
          disabled={isSubmitting}
        >
          {isSubmitting ? "Salvando..." : "Salvar Dados"}
        </Button>
      </form>
    </div>
  );
}
```

## 3. Formulário de Endereço com Busca por CEP

```typescript
import { useForm } from '@/hooks/form/useForm';
import { addressSchema } from '@/lib/validations/schemas/address';
import { FormField, FormSelect, MaskedFormField } from '@/components/forms';
import { fetchAddressByCEP } from '@/lib/validations/rules/cep';

const estados = [
  { value: "SE", label: "Sergipe" },
  { value: "BA", label: "Bahia" },
  { value: "AL", label: "Alagoas" }
];

export function AddressForm() {
  const {
    formData,
    errors,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
    setValue,
  } = useForm({
    initialValues: {
      cep: "",
      cidade: "",
      estado: "",
      logradouro: "",
      bairro: "",
      numero: "",
      complemento: ""
    },
    validation: {
      schema: addressSchema
    },
    callbacks: {
      onSubmit: async (values) => {
        console.log("Endereço:", values);
      }
    }
  });

  // Buscar endereço quando CEP for preenchido
  const handleCEPChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    handleChange(e);
    
    const cep = e.target.value.replace(/\D/g, '');
    if (cep.length === 8) {
      try {
        const addressInfo = await fetchAddressByCEP(cep);
        if (addressInfo) {
          setValue('cidade', addressInfo.localidade);
          setValue('estado', addressInfo.uf);
          setValue('logradouro', addressInfo.logradouro);
          setValue('bairro', addressInfo.bairro);
        }
      } catch (error) {
        console.error('Erro ao buscar CEP:', error);
      }
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">Endereço</h2>
      
      <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <MaskedFormField
          label="CEP"
          name="cep"
          value={formData.cep || ""}
          onChange={handleCEPChange}
          onBlur={handleBlur}
          mask="cep"
          error={errors.cep}
          placeholder="00000-000"
        />

        <FormField
          label="Cidade"
          name="cidade"
          value={formData.cidade || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.cidade}
        />

        <FormSelect
          label="Estado"
          name="estado"
          value={formData.estado || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          options={estados}
          error={errors.estado}
        />

        <FormField
          label="Logradouro"
          name="logradouro"
          value={formData.logradouro || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.logradouro}
          className="md:col-span-2"
        />

        <FormField
          label="Bairro"
          name="bairro"
          value={formData.bairro || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.bairro}
        />

        <FormField
          label="Número"
          name="numero"
          value={formData.numero || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.numero}
        />

        <FormField
          label="Complemento"
          name="complemento"
          value={formData.complemento || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.complemento}
          className="md:col-span-2"
          placeholder="Apartamento, bloco, etc."
        />

        <Button
          type="submit"
          className="md:col-span-2"
          isLoading={isSubmitting}
          disabled={isSubmitting}
        >
          {isSubmitting ? "Salvando..." : "Salvar Endereço"}
        </Button>
      </form>
    </div>
  );
}
```

## 4. Formulário de Mudança de Senha

```typescript
import { useForm } from '@/hooks/form/useForm';
import { changePasswordSchema } from '@/lib/validations/schemas/auth';
import { FormField, Button } from '@/components/forms';
import { analyzePasswordStrength } from '@/lib/validations/rules/password';
import { useState } from 'react';

export function ChangePasswordForm() {
  const [passwordStrength, setPasswordStrength] = useState<any>(null);

  const {
    formData,
    errors,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
  } = useForm({
    initialValues: {
      senhaAtual: "",
      novaSenha: "",
      confirmarNovaSenha: ""
    },
    validation: {
      schema: changePasswordSchema
    },
    callbacks: {
      onSubmit: async (values) => {
        try {
          const response = await fetch('/api/auth/change-password', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(values)
          });
          
          if (response.ok) {
            alert('Senha alterada com sucesso!');
          } else {
            throw new Error('Erro ao alterar senha');
          }
        } catch (error) {
          alert('Erro: ' + error.message);
        }
      }
    }
  });

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleChange(e);
    
    if (e.target.name === 'novaSenha') {
      const analysis = analyzePasswordStrength(e.target.value);
      setPasswordStrength(analysis);
    }
  };

  const getStrengthColor = (strength: string) => {
    switch (strength) {
      case 'weak': return 'text-red-500';
      case 'medium': return 'text-yellow-500';
      case 'strong': return 'text-green-500';
      default: return 'text-gray-500';
    }
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">Alterar Senha</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <FormField
          label="Senha Atual"
          name="senhaAtual"
          type="password"
          value={formData.senhaAtual || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.senhaAtual}
          required
        />

        <FormField
          label="Nova Senha"
          name="novaSenha"
          type="password"
          value={formData.novaSenha || ""}
          onChange={handlePasswordChange}
          onBlur={handleBlur}
          error={errors.novaSenha}
          required
        />

        {passwordStrength && formData.novaSenha && (
          <div className="text-sm">
            <div className={`font-medium ${getStrengthColor(passwordStrength.strength)}`}>
              Força da senha: {passwordStrength.strength === 'weak' ? 'Fraca' : 
                              passwordStrength.strength === 'medium' ? 'Média' : 'Forte'}
            </div>
            <div className="text-gray-600 mt-1">
              {passwordStrength.feedback.join(', ')}
            </div>
          </div>
        )}

        <FormField
          label="Confirmar Nova Senha"
          name="confirmarNovaSenha"
          type="password"
          value={formData.confirmarNovaSenha || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.confirmarNovaSenha}
          required
        />

        <Button
          type="submit"
          className="w-full"
          isLoading={isSubmitting}
          disabled={isSubmitting}
        >
          {isSubmitting ? "Alterando..." : "Alterar Senha"}
        </Button>
      </form>
    </div>
  );
}
```

## 5. Formulário com Validação Customizada

```typescript
import { useForm } from '@/hooks/form/useForm';
import { z } from 'zod';
import { FormField, Button } from '@/components/forms';

// Schema customizado para um formulário específico
const customSchema = z.object({
  nomeEvento: z.string()
    .min(3, "Nome deve ter pelo menos 3 caracteres")
    .max(100, "Nome muito longo"),
  dataEvento: z.string()
    .refine((date) => {
      const eventDate = new Date(date);
      const today = new Date();
      return eventDate > today;
    }, "Data deve ser futura"),
  participantes: z.number()
    .min(1, "Deve ter pelo menos 1 participante")
    .max(1000, "Máximo 1000 participantes"),
  categoria: z.enum(["workshop", "palestra", "curso", "seminario"])
});

export function EventForm() {
  const {
    formData,
    errors,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
  } = useForm({
    initialValues: {
      nomeEvento: "",
      dataEvento: "",
      participantes: 0,
      categoria: ""
    },
    validation: {
      schema: customSchema
    },
    callbacks: {
      onSubmit: async (values) => {
        console.log("Evento criado:", values);
      }
    }
  });

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">Criar Evento</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <FormField
          label="Nome do Evento"
          name="nomeEvento"
          value={formData.nomeEvento || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.nomeEvento}
          required
        />

        <FormField
          label="Data do Evento"
          name="dataEvento"
          type="date"
          value={formData.dataEvento || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.dataEvento}
          required
        />

        <FormField
          label="Número de Participantes"
          name="participantes"
          type="number"
          value={formData.participantes?.toString() || ""}
          onChange={(e) => {
            const value = parseInt(e.target.value) || 0;
            handleChange({ ...e, target: { ...e.target, value } });
          }}
          onBlur={handleBlur}
          error={errors.participantes}
          required
        />

        <FormSelect
          label="Categoria"
          name="categoria"
          value={formData.categoria || ""}
          onChange={handleChange}
          onBlur={handleBlur}
          options={[
            { value: "workshop", label: "Workshop" },
            { value: "palestra", label: "Palestra" },
            { value: "curso", label: "Curso" },
            { value: "seminario", label: "Seminário" }
          ]}
          error={errors.categoria}
          required
        />

        <Button
          type="submit"
          className="w-full"
          isLoading={isSubmitting}
          disabled={isSubmitting}
        >
          {isSubmitting ? "Criando..." : "Criar Evento"}
        </Button>
      </form>
    </div>
  );
}
```

## Dicas de Uso

### 1. Sempre use `|| ""` nos valores
```typescript
value={formData.email || ""}
```

### 2. Adicione `onBlur` para validação em tempo real
```typescript
onBlur={handleBlur}
```

### 3. Use `isLoading` e `disabled` nos botões
```typescript
<Button
  isLoading={isSubmitting}
  disabled={isSubmitting}
>
  {isSubmitting ? "Salvando..." : "Salvar"}
</Button>
```

### 4. Para campos numéricos, converta o valor
```typescript
onChange={(e) => {
  const value = parseInt(e.target.value) || 0;
  handleChange({ ...e, target: { ...e.target, value } });
}}
```

### 5. Use máscaras para formatação automática
```typescript
<MaskedFormField mask="cpf" ... />
<MaskedFormField mask="phone" ... />
<MaskedFormField mask="cep" ... />
```
