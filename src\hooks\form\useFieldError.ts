import { useState, useCallback, useRef, useEffect } from "react";

/**
 * Hook para gerenciar erros de campo individual
 */
export function useFieldError(initialError?: string) {
  const [error, setError] = useState<string | undefined>(initialError);
  const [isDirty, setIsDirty] = useState(false);
  const [isTouched, setIsTouched] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  /**
   * Define um erro para o campo
   */
  const setFieldError = useCallback((errorMessage?: string) => {
    setError(errorMessage);
  }, []);

  /**
   * Limpa o erro do campo
   */
  const clearError = useCallback(() => {
    setError(undefined);
  }, []);

  /**
   * Marca o campo como "sujo" (modificado)
   */
  const markAsDirty = useCallback(() => {
    setIsDirty(true);
  }, []);

  /**
   * Marca o campo como "tocado" (focado)
   */
  const markAsTouched = useCallback(() => {
    setIsTouched(true);
  }, []);

  /**
   * Define erro com debounce
   */
  const setErrorWithDebounce = useCallback(
    (errorMessage?: string, delay = 300) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        setError(errorMessage);
      }, delay);
    },
    []
  );

  /**
   * Reseta o estado do campo
   */
  const reset = useCallback(() => {
    setError(undefined);
    setIsDirty(false);
    setIsTouched(false);

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  /**
   * Verifica se deve mostrar o erro
   * Por padrão, só mostra erro se o campo foi tocado ou está sujo
   */
  const shouldShowError = useCallback(
    (showOnlyAfterTouch = true) => {
      if (!error) return false;
      if (!showOnlyAfterTouch) return true;
      return isTouched || isDirty;
    },
    [error, isTouched, isDirty]
  );

  // Cleanup do timeout quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    error,
    isDirty,
    isTouched,
    hasError: !!error,
    isValid: !error,
    setFieldError,
    clearError,
    markAsDirty,
    markAsTouched,
    setErrorWithDebounce,
    reset,
    shouldShowError,
  };
}

/**
 * Hook para gerenciar múltiplos erros de campo
 */
export function useFieldErrors<T extends Record<string, unknown>>(
  initialErrors?: Partial<Record<keyof T, string>>
) {
  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>(
    initialErrors || {}
  );
  const [touchedFields, setTouchedFields] = useState<Set<keyof T>>(new Set());
  const [dirtyFields, setDirtyFields] = useState<Set<keyof T>>(new Set());

  /**
   * Define erro para um campo específico
   */
  const setFieldError = useCallback((fieldName: keyof T, error?: string) => {
    setErrors((prev) => ({
      ...prev,
      [fieldName]: error,
    }));
  }, []);

  /**
   * Define múltiplos erros
   */
  const setFieldErrors = useCallback(
    (newErrors: Partial<Record<keyof T, string>>) => {
      setErrors((prev) => ({
        ...prev,
        ...newErrors,
      }));
    },
    []
  );

  /**
   * Limpa erro de um campo específico
   */
  const clearFieldError = useCallback((fieldName: keyof T) => {
    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[fieldName];
      return newErrors;
    });
  }, []);

  /**
   * Limpa todos os erros
   */
  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  /**
   * Marca campo como tocado
   */
  const markFieldAsTouched = useCallback((fieldName: keyof T) => {
    setTouchedFields((prev) => new Set(prev).add(fieldName));
  }, []);

  /**
   * Marca campo como sujo
   */
  const markFieldAsDirty = useCallback((fieldName: keyof T) => {
    setDirtyFields((prev) => new Set(prev).add(fieldName));
  }, []);

  /**
   * Verifica se um campo tem erro
   */
  const hasFieldError = useCallback(
    (fieldName: keyof T) => {
      return !!errors[fieldName];
    },
    [errors]
  );

  /**
   * Obtém erro de um campo
   */
  const getFieldError = useCallback(
    (fieldName: keyof T) => {
      return errors[fieldName];
    },
    [errors]
  );

  /**
   * Verifica se deve mostrar erro para um campo
   */
  const shouldShowFieldError = useCallback(
    (fieldName: keyof T, showOnlyAfterTouch = true) => {
      const hasError = hasFieldError(fieldName);
      if (!hasError) return false;
      if (!showOnlyAfterTouch) return true;

      return touchedFields.has(fieldName) || dirtyFields.has(fieldName);
    },
    [hasFieldError, touchedFields, dirtyFields]
  );

  /**
   * Reseta todos os estados
   */
  const reset = useCallback(() => {
    setErrors({});
    setTouchedFields(new Set());
    setDirtyFields(new Set());
  }, []);

  return {
    errors,
    touchedFields,
    dirtyFields,
    hasErrors: Object.keys(errors).length > 0,
    setFieldError,
    setFieldErrors,
    clearFieldError,
    clearAllErrors,
    markFieldAsTouched,
    markFieldAsDirty,
    hasFieldError,
    getFieldError,
    shouldShowFieldError,
    reset,
  };
}
