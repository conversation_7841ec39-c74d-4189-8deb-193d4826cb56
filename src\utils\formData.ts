export const generos = [
  { value: 'masculino', label: '<PERSON><PERSON><PERSON><PERSON>' },
  { value: 'feminino', label: '<PERSON><PERSON><PERSON>' },
  { value: 'outro', label: 'Outro' },
  { value: 'nao_informar', label: 'Prefiro não informar' }
];

export const estados = [
  { value: 'AC', label: 'Acre' },
  { value: 'AL', label: 'Alagoas' },
  { value: 'AP', label: 'Amapá' },
  { value: 'AM', label: 'Amazonas' },
  { value: 'BA', label: 'Bahia' },
  { value: 'CE', label: 'Ceará' },
  { value: 'DF', label: 'Distrito Federal' },
  { value: 'ES', label: 'Espírito Santo' },
  { value: 'GO', label: 'Goiás' },
  { value: 'MA', label: 'Maranhão' },
  { value: 'MT', label: 'Mato Grosso' },
  { value: 'MS', label: 'Mato Grosso do Sul' },
  { value: 'MG', label: 'Minas Gerais' },
  { value: 'PA', label: '<PERSON>r<PERSON>' },
  { value: 'PB', label: 'Paraíba' },
  { value: 'PR', label: 'Paraná' },
  { value: 'PE', label: 'Pernambuco' },
  { value: 'PI', label: 'Piauí' },
  { value: 'RJ', label: 'Rio de Janeiro' },
  { value: 'RN', label: 'Rio Grande do Norte' },
  { value: 'RS', label: 'Rio Grande do Sul' },
  { value: 'RO', label: 'Rondônia' },
  { value: 'RR', label: 'Roraima' },
  { value: 'SC', label: 'Santa Catarina' },
  { value: 'SP', label: 'São Paulo' },
  { value: 'SE', label: 'Sergipe' },
  { value: 'TO', label: 'Tocantins' }
];

export const escolaridades = [
  { value: 'fundamental_incompleto', label: 'Ensino Fundamental Incompleto' },
  { value: 'fundamental_completo', label: 'Ensino Fundamental Completo' },
  { value: 'medio_incompleto', label: 'Ensino Médio Incompleto' },
  { value: 'medio_completo', label: 'Ensino Médio Completo' },
  { value: 'superior_incompleto', label: 'Ensino Superior Incompleto' },
  { value: 'superior_completo', label: 'Ensino Superior Completo' },
  { value: 'pos_graduacao', label: 'Pós-graduação' },
  { value: 'mestrado', label: 'Mestrado' },
  { value: 'doutorado', label: 'Doutorado' }
];

export const rendas = [
  { value: 'ate_1', label: 'Até 1 salário mínimo' },
  { value: '1_a_2', label: 'De 1 a 2 salários mínimos' },
  { value: '2_a_3', label: 'De 2 a 3 salários mínimos' },
  { value: '3_a_5', label: 'De 3 a 5 salários mínimos' },
  { value: '5_a_10', label: 'De 5 a 10 salários mínimos' },
  { value: 'acima_10', label: 'Acima de 10 salários mínimos' },
  { value: 'nao_informar', label: 'Prefiro não informar' }
];
