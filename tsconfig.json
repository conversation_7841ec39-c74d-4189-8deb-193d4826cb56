{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@/components": ["./src/components"], "@/components/*": ["./src/components/*"], "@/contexts": ["./src/contexts"], "@/contexts/*": ["./src/contexts/*"], "@/hooks": ["./src/hooks"], "@/hooks/*": ["./src/hooks/*"], "@/utils": ["./src/utils"], "@/utils/*": ["./src/utils/*"], "@/services": ["./src/services"], "@/services/*": ["./src/services/*"], "@/types": ["./src/types"], "@/types/*": ["./src/types/*"], "@/lib": ["./src/lib"], "@/lib/*": ["./src/lib/*"], "@/styles": ["./src/styles"], "@/styles/*": ["./src/styles/*"], "@/constants": ["./src/constants"], "@/constants/*": ["./src/constants/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}