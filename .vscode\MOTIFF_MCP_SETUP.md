# Configuração do Motiff MCP Server no VS Code

Este guia explica como configurar o Motiff MCP Server no VS Code para integrar designs do Motiff com IA.

## Pré-requisitos

- Node.js versão 18 ou superior
- VS Code com suporte ao MCP
- Conta no Motiff com acesso ao Develop Mode

## Passos para Configuração

### 1. Verificar Node.js
```bash
node --version
```
Certifique-se de que a versão seja 18 ou superior.

### 2. Instalar o Motiff MCP Server
```bash
npm install -g @motiffcom/motiff-mcp-server
```

### 3. Configurar Token do Motiff
Você precisa configurar o token de acesso do Motiff:

1. **Obter o token no Motiff:**
   - Acesse sua conta no Motiff
   - Vá para configurações de desenvolvedor
   - Gere um token de API

2. **Configurar variável de ambiente:**
   - Windows: `setx MOTIFF_TOKEN "seu_token_aqui"`
   - macOS/Linux: `export MOTIFF_TOKEN="seu_token_aqui"`

   Ou adicione ao seu arquivo `.env` local (não commitado):
   ```
   MOTIFF_TOKEN=seu_token_aqui
   ```

### 4. Configuração no VS Code
O arquivo `.vscode/mcp.json` já foi criado com a configuração necessária:

```json
{
  "mcpServers": {
    "motiff": {
      "command": "npx",
      "args": [
        "@motiffcom/motiff-mcp-server"
      ],
      "env": {
        "MOTIFF_TOKEN": "${env:MOTIFF_TOKEN}"
      }
    }
  }
}
```

### 5. Configuração no Motiff
1. Abra seu arquivo no Motiff
2. Ative o Develop Mode (⇧ Shift + D)
3. Vá em **Main menu** → **Help and account** → **Motiff MCP Server**
4. Siga as instruções específicas para VS Code

### 6. Verificar a Configuração
1. Reinicie o VS Code após a configuração
2. Abra o Command Palette no VS Code (Ctrl+Shift+P)
3. Execute o comando: `MCP: List Servers`
4. Verifique se o servidor "motiff" aparece na lista

### 7. Usar o MCP Server
1. Abra o chat da IA no VS Code
2. Cole um link para um frame do Motiff
3. Forneça instruções como:
   - "Ajude-me a criar um componente React baseado neste design"
   - "Gere código HTML/CSS para este frame"

## Solução de Problemas

### Servidor não aparece na lista
- Reinicie o VS Code
- Verifique se o Node.js está na versão correta
- Verifique se o pacote foi instalado corretamente

### Erro ao executar o servidor
- Execute: `npm install -g @motiffcom/motiff-mcp-server` novamente
- Verifique as permissões do sistema

### Design não é interpretado corretamente
- Reduza a complexidade do frame exportado
- Tente exportar apenas as camadas necessárias
- Use designs menos complexos para melhores resultados

## Recursos Adicionais
 
- [Documentação oficial do Motiff MCP](https://motiff.com/help/docs/sections/358051399613186)
- [Model Context Protocol](https://modelcontextprotocol.io/introduction)
