import React from "react";

interface PersonTypeSelectorProps {
  value: "fisica" | "juridica" | "";
  onChange: (type: "fisica" | "juridica") => void;
  error?: string;
}

export function PersonTypeSelector({
  value,
  onChange,
  error,
}: PersonTypeSelectorProps) {
  return (
    <div className="mb-7">
      <h3 className="text-base font-medium text-[#4A5568] mb-3">
        Tipo de Pessoa
      </h3>
      <div
        className="flex gap-4 relative"
        role="radiogroup"
        aria-label="Tipo de pessoa"
      >
        {/* Pessoa Física */}
        <div
          className={`flex-1 border-2 rounded-lg p-[18px] cursor-pointer transition-colors ${
            value === "fisica"
              ? "border-[#E0E9F7] bg-white"
              : "border-[#E0E9F7] bg-white hover:border-[#d1d9e6]"
          }`}
          onClick={() => onChange("fisica")}
          role="radio"
          aria-checked={value === "fisica"}
          aria-label="Pessoa Física"
        >
          <div className="flex items-center gap-2">
            <div className="relative">
              {/* Radio button customizado */}
              <div
                className={`w-[17px] h-[17px] rounded-full border-2 flex items-center justify-center ${
                  value === "fisica"
                    ? "border-[#4070F7] bg-[#4070F7]"
                    : "border-[#989898] bg-white"
                }`}
              >
                {value === "fisica" && (
                  <div className="w-[5px] h-[5px] bg-white rounded-full"></div>
                )}
              </div>
            </div>
            <svg
              className="w-4 h-4 text-[#1B3B6F]"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
            </svg>
            <span className="text-base font-normal text-black">
              Pessoa Física
            </span>
          </div>
        </div>

        {/* Pessoa Jurídica */}
        <div
          className={`flex-1 border-2 rounded-lg p-[18px] cursor-pointer transition-colors ${
            value === "juridica"
              ? "border-[#E0E9F7] bg-white"
              : "border-[#E0E9F7] bg-white hover:border-[#d1d9e6]"
          }`}
          onClick={() => onChange("juridica")}
          role="radio"
          aria-checked={value === "juridica"}
          aria-label="Pessoa Jurídica"
        >
          <div className="flex items-center gap-2">
            <div className="relative">
              {/* Radio button customizado */}
              <div
                className={`w-[17px] h-[17px] rounded-full border-2 flex items-center justify-center ${
                  value === "juridica"
                    ? "border-[#4070F7] bg-[#4070F7]"
                    : "border-[#989898] bg-white"
                }`}
              >
                {value === "juridica" && (
                  <div className="w-[5px] h-[5px] bg-white rounded-full"></div>
                )}
              </div>
            </div>
            <svg
              className="w-4 h-4 text-[#1B3B6F]"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M12 7V3H2v18h20V7H12zM6 19H4v-2h2v2zm0-4H4v-2h2v2zm0-4H4V9h2v2zm0-4H4V5h2v2zm4 12H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V9h2v2zm0-4H8V5h2v2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8v10zm-2-8h-2v2h2v-2zm0 4h-2v2h2v-2z" />
            </svg>
            <span className="text-base font-normal text-black">
              Pessoa Jurídica
            </span>
          </div>
        </div>
      </div>
      {error && <span className="text-xs text-red-500 mt-1">{error}</span>}
    </div>
  );
}
