import React from "react";
import { Input } from "@heroui/react";

export interface BaseInputProps {
  label: string;
  name: string;
  type?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
  className?: string;
  inputClassName?: string;
  labelClassName?: string;
  errorClassName?: string;
  showErrorIcon?: boolean;
  autoComplete?: string;
  maxLength?: number;
  minLength?: number;
}

/**
 * Componente base para inputs com bordas finas e melhor integração com validação
 */
export function BaseInput({
  label,
  name,
  type = "text",
  value,
  onChange,
  onBlur,
  placeholder,
  required = false,
  error,
  disabled = false,
  className = "",
  inputClassName = "",
  labelClassName = "",
  errorClassName = "",
  showErrorIcon = true,
  autoComplete,
  maxLength,
  minLength,
}: BaseInputProps) {
  const hasError = !!error;

  return (
    <div className={`flex flex-col ${className}`}>
      {/* Label */}
      <label
        htmlFor={name}
        className={`text-base font-medium text-[#4A5568] mb-1 ${labelClassName}`}
      >
        {label} {required && <span className="text-[#FF4B4B]">*</span>}
      </label>

      {/* Input */}
      <Input
        type={type}
        id={name}
        name={name}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        placeholder={placeholder}
        disabled={disabled}
        autoComplete={autoComplete}
        maxLength={maxLength}
        minLength={minLength}
        variant="bordered"
        radius="sm"
        size="lg"
        classNames={{
          input: `text-black font-normal ${inputClassName}`,
          inputWrapper: [
            "bg-white",
            // Bordas mais finas (1px ao invés de 2px)
            "border",
            "border-[#E0E9F7]",
            "hover:border-[#d1d9e6]",
            "focus-within:!border-[#4070F7]",
            "h-[40px]",
            "min-h-[40px]",
            "rounded-md",
            // Estados de erro
            hasError ? "!border-red-500 hover:!border-red-500" : "",
            // Estado desabilitado
            disabled ? "opacity-60 cursor-not-allowed" : "",
            // Transições suaves
            "transition-colors duration-200",
          ]
            .filter(Boolean)
            .join(" "),
        }}
      />

      {/* Mensagem de erro */}
      {hasError && (
        <div className={`flex items-center mt-1 ${errorClassName}`}>
          {showErrorIcon && (
            <svg
              className="w-3 h-3 text-red-500 mr-1 flex-shrink-0"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
          )}
          <span className="text-xs text-red-500">{error}</span>
        </div>
      )}
    </div>
  );
}

/**
 * Variante do BaseInput para senhas com toggle de visibilidade
 */
export function BasePasswordInput(props: Omit<BaseInputProps, 'type'>) {
  const [isVisible, setIsVisible] = React.useState(false);

  const toggleVisibility = () => setIsVisible(!isVisible);

  return (
    <div className={`flex flex-col ${props.className}`}>
      {/* Label */}
      <label
        htmlFor={props.name}
        className={`text-base font-medium text-[#4A5568] mb-1 ${props.labelClassName}`}
      >
        {props.label} {props.required && <span className="text-[#FF4B4B]">*</span>}
      </label>

      {/* Input com botão de toggle */}
      <Input
        type={isVisible ? "text" : "password"}
        id={props.name}
        name={props.name}
        value={props.value}
        onChange={props.onChange}
        onBlur={props.onBlur}
        placeholder={props.placeholder}
        disabled={props.disabled}
        autoComplete={props.autoComplete}
        maxLength={props.maxLength}
        minLength={props.minLength}
        variant="bordered"
        radius="sm"
        size="lg"
        endContent={
          <button
            className="focus:outline-none"
            type="button"
            onClick={toggleVisibility}
            aria-label={isVisible ? "Ocultar senha" : "Mostrar senha"}
          >
            {isVisible ? (
              <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z"
                  clipRule="evenodd"
                />
                <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
              </svg>
            ) : (
              <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                <path
                  fillRule="evenodd"
                  d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                  clipRule="evenodd"
                />
              </svg>
            )}
          </button>
        }
        classNames={{
          input: `text-black font-normal ${props.inputClassName}`,
          inputWrapper: [
            "bg-white",
            "border",
            "border-[#E0E9F7]",
            "hover:border-[#d1d9e6]",
            "focus-within:!border-[#4070F7]",
            "h-[40px]",
            "min-h-[40px]",
            "rounded-md",
            props.error ? "!border-red-500 hover:!border-red-500" : "",
            props.disabled ? "opacity-60 cursor-not-allowed" : "",
            "transition-colors duration-200",
          ]
            .filter(Boolean)
            .join(" "),
        }}
      />

      {/* Mensagem de erro */}
      {props.error && (
        <div className={`flex items-center mt-1 ${props.errorClassName}`}>
          {props.showErrorIcon && (
            <svg
              className="w-3 h-3 text-red-500 mr-1 flex-shrink-0"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
          )}
          <span className="text-xs text-red-500">{props.error}</span>
        </div>
      )}
    </div>
  );
}
