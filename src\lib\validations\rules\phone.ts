/**
 * Validação de telefone e celular
 */

/**
 * Remove caracteres não numéricos de uma string
 */
function removeNonNumeric(value: string): string {
  return value.replace(/\D/g, '');
}

/**
 * Valida telefone fixo (formato: (XX) XXXX-XXXX)
 */
export function validatePhone(phone: string): boolean {
  const cleanPhone = removeNonNumeric(phone);
  
  // Telefone fixo deve ter 10 dígitos (2 do DDD + 8 do número)
  if (cleanPhone.length !== 10) return false;
  
  // Verifica se o DDD é válido (11 a 99)
  const ddd = parseInt(cleanPhone.substring(0, 2));
  if (ddd < 11 || ddd > 99) return false;
  
  // Primeiro dígito do número não pode ser 0 ou 1
  const firstDigit = parseInt(cleanPhone.charAt(2));
  if (firstDigit < 2) return false;
  
  return true;
}

/**
 * Valida celular (formato: (XX) 9XXXX-XXXX)
 */
export function validateCellPhone(cellPhone: string): boolean {
  const cleanCellPhone = removeNonNumeric(cellPhone);
  
  // Celular deve ter 11 dígitos (2 do DDD + 9 do número)
  if (cleanCellPhone.length !== 11) return false;
  
  // Verifica se o DDD é válido (11 a 99)
  const ddd = parseInt(cleanCellPhone.substring(0, 2));
  if (ddd < 11 || ddd > 99) return false;
  
  // Terceiro dígito deve ser 9 (celular)
  if (cleanCellPhone.charAt(2) !== '9') return false;
  
  // Quarto dígito não pode ser 0 ou 1
  const fourthDigit = parseInt(cleanCellPhone.charAt(3));
  if (fourthDigit < 2) return false;
  
  return true;
}

/**
 * Valida telefone ou celular (aceita ambos os formatos)
 */
export function validatePhoneOrCell(phone: string): boolean {
  const cleanPhone = removeNonNumeric(phone);
  
  // Aceita tanto telefone (10 dígitos) quanto celular (11 dígitos)
  if (cleanPhone.length === 10) {
    return validatePhone(phone);
  } else if (cleanPhone.length === 11) {
    return validateCellPhone(phone);
  }
  
  return false;
}

/**
 * Formata telefone para exibição
 */
export function formatPhone(phone: string): string {
  const cleanPhone = removeNonNumeric(phone);
  
  if (cleanPhone.length === 10) {
    // Telefone fixo: (XX) XXXX-XXXX
    return cleanPhone.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
  } else if (cleanPhone.length === 11) {
    // Celular: (XX) 9XXXX-XXXX
    return cleanPhone.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  }
  
  return phone;
}

/**
 * Determina se um número é celular ou telefone fixo
 */
export function getPhoneType(phone: string): 'cellphone' | 'phone' | 'invalid' {
  const cleanPhone = removeNonNumeric(phone);
  
  if (cleanPhone.length === 10 && validatePhone(phone)) {
    return 'phone';
  } else if (cleanPhone.length === 11 && validateCellPhone(phone)) {
    return 'cellphone';
  }
  
  return 'invalid';
}
