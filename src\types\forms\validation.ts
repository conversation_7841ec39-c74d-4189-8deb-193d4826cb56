import { z } from "zod";

/**
 * Tipo genérico para erros de validação
 */
export type ValidationErrors<T = Record<string, unknown>> = Partial<
  Record<keyof T, string>
>;

/**
 * Estado de validação de um campo
 */
export type FieldValidationState = {
  isValid: boolean;
  error?: string;
  isDirty: boolean;
  isTouched: boolean;
};

/**
 * Estado de validação de um formulário
 */
export type FormValidationState<T = Record<string, unknown>> = {
  isValid: boolean;
  errors: ValidationErrors<T>;
  fields: Record<keyof T, FieldValidationState>;
  isSubmitting: boolean;
  isDirty: boolean;
};

/**
 * Configuração de validação para um campo
 */
export type FieldValidationConfig = {
  required?: boolean;
  schema?: z.ZodSchema;
  customValidator?: (value: unknown) => string | undefined;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  debounceMs?: number;
};

/**
 * Configuração de validação para um formulário
 */
export type FormValidationConfig<T = Record<string, unknown>> = {
  schema?: z.ZodSchema<T>;
  fields?: Partial<Record<keyof T, FieldValidationConfig>>;
  validateOnSubmit?: boolean;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  stopOnFirstError?: boolean;
};

/**
 * Resultado de uma validação
 */
export type ValidationResult = {
  isValid: boolean;
  error?: string;
};

/**
 * Resultado de validação de formulário
 */
export type FormValidationResult<T = Record<string, unknown>> = {
  isValid: boolean;
  errors: ValidationErrors<T>;
  data?: T;
};

/**
 * Opções para validação assíncrona
 */
export type AsyncValidationOptions = {
  debounceMs?: number;
  signal?: AbortSignal;
};

/**
 * Função de validação customizada
 */
export type CustomValidator<T = unknown> = (
  value: T,
  formData?: Record<string, unknown>
) => string | undefined | Promise<string | undefined>;

/**
 * Função de validação assíncrona
 */
export type AsyncValidator<T = unknown> = (
  value: T,
  options?: AsyncValidationOptions
) => Promise<ValidationResult>;

/**
 * Evento de mudança de campo
 */
export type FieldChangeEvent = {
  name: string;
  value: unknown;
  previousValue: unknown;
};

/**
 * Evento de validação
 */
export type ValidationEvent<T = Record<string, unknown>> = {
  type: "field" | "form";
  fieldName?: keyof T;
  isValid: boolean;
  errors: ValidationErrors<T>;
};

/**
 * Hook de validação - callbacks
 */
export type ValidationCallbacks<T = Record<string, unknown>> = {
  onFieldChange?: (event: FieldChangeEvent) => void;
  onFieldValidation?: (event: ValidationEvent<T>) => void;
  onFormValidation?: (event: ValidationEvent<T>) => void;
  onSubmit?: (data: T) => void | Promise<void>;
  onSubmitError?: (errors: ValidationErrors<T>) => void;
};

/**
 * Configuração completa para hook de formulário
 */
export type UseFormConfig<T = Record<string, unknown>> = {
  initialValues?: Partial<T>;
  validation?: FormValidationConfig<T>;
  callbacks?: ValidationCallbacks<T>;
  resetOnSubmit?: boolean;
  clearErrorsOnChange?: boolean;
};
