/**
 * Validação e formatação de CEP
 */

/**
 * Remove caracteres não numéricos de uma string
 */
function removeNonNumeric(value: string): string {
  return value.replace(/\D/g, "");
}

/**
 * Valida CEP brasileiro
 */
export function validateCEP(cep: string): boolean {
  if (!cep || typeof cep !== "string") return false;

  const cleanCEP = removeNonNumeric(cep);

  // CEP deve ter exatamente 8 dígitos
  if (cleanCEP.length !== 8) return false;

  // Verifica se não são todos os dígitos iguais
  if (/^(\d)\1{7}$/.test(cleanCEP)) return false;

  return true;
}

/**
 * Formata CEP para exibição (XXXXX-XXX)
 */
export function formatCEP(cep: string): string {
  const cleanCEP = removeNonNumeric(cep);

  if (cleanCEP.length !== 8) return cep;

  return cleanCEP.replace(/(\d{5})(\d{3})/, "$1-$2");
}

/**
 * Remove formatação do CEP
 */
export function unformatCEP(cep: string): string {
  return removeNonNumeric(cep);
}

/**
 * Valida e formata CEP
 */
export function validateAndFormatCEP(cep: string): {
  isValid: boolean;
  formatted: string;
  clean: string;
} {
  const clean = unformatCEP(cep);
  const isValid = validateCEP(cep);
  const formatted = isValid ? formatCEP(cep) : cep;

  return {
    isValid,
    formatted,
    clean,
  };
}

/**
 * Máscara para input de CEP
 */
export function applyCEPMask(value: string): string {
  const clean = removeNonNumeric(value);

  // Limita a 8 dígitos
  const limited = clean.slice(0, 8);

  // Aplica máscara progressiva
  if (limited.length <= 5) {
    return limited;
  }

  return limited.replace(/(\d{5})(\d{0,3})/, "$1-$2");
}

/**
 * Busca informações de endereço por CEP (usando ViaCEP)
 */
export async function fetchAddressByCEP(cep: string): Promise<{
  success: boolean;
  data?: {
    cep: string;
    logradouro: string;
    complemento: string;
    bairro: string;
    localidade: string;
    uf: string;
    ibge: string;
    gia: string;
    ddd: string;
    siafi: string;
  };
  error?: string;
}> {
  if (!validateCEP(cep)) {
    return {
      success: false,
      error: "CEP inválido",
    };
  }

  const cleanCEP = unformatCEP(cep);

  try {
    const response = await fetch(`https://viacep.com.br/ws/${cleanCEP}/json/`);

    if (!response.ok) {
      throw new Error("Erro na consulta do CEP");
    }

    const data = await response.json();

    if (data.erro) {
      return {
        success: false,
        error: "CEP não encontrado",
      };
    }

    return {
      success: true,
      data,
    };
  } catch {
    return {
      success: false,
      error: "Erro ao consultar CEP",
    };
  }
}
