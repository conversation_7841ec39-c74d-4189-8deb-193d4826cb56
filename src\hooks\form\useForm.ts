import { useState, useCallback, useMemo } from "react";
import { useValidation } from "./useValidation";
import { useFieldErrors } from "./useFieldError";
import {
  UseFormConfig,
  ValidationErrors,
  FormValidationResult,
} from "@/types/forms";

/**
 * Hook principal para gerenciamento de formulários com validação
 */
export function useForm<T extends Record<string, unknown>>(
  config?: UseFormConfig<T>
) {
  const {
    initialValues = {} as Partial<T>,
    validation,
    callbacks,
    resetOnSubmit = false,
    clearErrorsOnChange = true,
  } = config || {};

  // Estado do formulário
  const [formData, setFormData] = useState<Partial<T>>(initialValues);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Hooks de validação e erros
  const { validateForm, isFieldValid, getFieldError } = useValidation<T>(
    validation?.schema
  );

  const {
    errors,
    setFieldError,
    setFieldErrors,
    clearFieldError,
    clearAllErrors,
    markFieldAsTouched,
    markFieldAsDirty,
    hasFieldError,
    shouldShowFieldError,
    reset: resetErrors,
  } = useFieldErrors<T>();

  /**
   * Atualiza valor de um campo
   */
  const setValue = useCallback(
    (name: keyof T, value: T[keyof T]) => {
      const previousValue = formData[name];

      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));

      // Marca campo como sujo
      markFieldAsDirty(name);

      // Limpa erro se configurado
      if (clearErrorsOnChange && hasFieldError(name)) {
        clearFieldError(name);
      }

      // Validação em tempo real se configurada
      if (
        validation?.validateOnChange ||
        validation?.fields?.[name]?.validateOnChange
      ) {
        const fieldError = getFieldError(name, value, {
          ...formData,
          [name]: value,
        });
        if (fieldError) {
          setFieldError(name, fieldError);
        }
      }

      // Callback de mudança
      callbacks?.onFieldChange?.({
        name: name as string,
        value,
        previousValue,
      });
    },
    [
      formData,
      markFieldAsDirty,
      clearErrorsOnChange,
      hasFieldError,
      clearFieldError,
      validation,
      getFieldError,
      setFieldError,
      callbacks,
    ]
  );

  /**
   * Atualiza múltiplos valores
   */
  const setValues = useCallback(
    (values: Partial<T>) => {
      setFormData((prev) => ({
        ...prev,
        ...values,
      }));

      // Marca campos como sujos
      Object.keys(values).forEach((key) => {
        markFieldAsDirty(key as keyof T);
      });

      // Limpa erros se configurado
      if (clearErrorsOnChange) {
        Object.keys(values).forEach((key) => {
          if (hasFieldError(key as keyof T)) {
            clearFieldError(key as keyof T);
          }
        });
      }
    },
    [markFieldAsDirty, clearErrorsOnChange, hasFieldError, clearFieldError]
  );

  /**
   * Manipula mudança de campo (para usar em inputs)
   */
  const handleChange = useCallback(
    (
      e: React.ChangeEvent<
        HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
      >
    ) => {
      const { name, value, type } = e.target;

      // Converte valor baseado no tipo do input
      let processedValue: unknown = value;

      if (type === "checkbox") {
        processedValue = (e.target as HTMLInputElement).checked;
      } else if (type === "number") {
        processedValue = value === "" ? "" : Number(value);
      }

      setValue(name as keyof T, processedValue as T[keyof T]);
    },
    [setValue]
  );

  /**
   * Manipula blur de campo
   */
  const handleBlur = useCallback(
    (
      e: React.FocusEvent<
        HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
      >
    ) => {
      const { name, value } = e.target;
      const fieldName = name as keyof T;

      // Marca campo como tocado
      markFieldAsTouched(fieldName);

      // Validação no blur se configurada
      if (
        validation?.validateOnBlur ||
        validation?.fields?.[fieldName]?.validateOnBlur
      ) {
        const fieldError = getFieldError(fieldName, value, formData);
        if (fieldError) {
          setFieldError(fieldName, fieldError);
        }
      }
    },
    [markFieldAsTouched, validation, getFieldError, formData, setFieldError]
  );

  /**
   * Valida o formulário
   */
  const validate = useCallback((): FormValidationResult<T> => {
    const result = validateForm(formData);

    if (!result.isValid && result.errors) {
      setFieldErrors(result.errors);
    }

    return result;
  }, [validateForm, formData, setFieldErrors]);

  /**
   * Reseta o formulário
   */
  const reset = useCallback(() => {
    setFormData(initialValues);
    resetErrors();
    setIsSubmitting(false);
  }, [initialValues, resetErrors]);

  /**
   * Submete o formulário
   */
  const handleSubmit = useCallback(
    async (e?: React.FormEvent) => {
      e?.preventDefault();

      if (isSubmitting) return;

      setIsSubmitting(true);

      try {
        // Valida formulário
        const validationResult = validate();

        if (!validationResult.isValid) {
          callbacks?.onSubmitError?.(validationResult.errors);
          return;
        }

        // Chama callback de submit
        if (callbacks?.onSubmit && validationResult.data) {
          await callbacks.onSubmit(validationResult.data);
        }

        // Reset se configurado
        if (resetOnSubmit) {
          reset();
        }
      } catch (error) {
        console.error("Erro no submit:", error);
        callbacks?.onSubmitError?.({
          _form: "Erro interno",
        } as ValidationErrors<T>);
      } finally {
        setIsSubmitting(false);
      }
    },
    [isSubmitting, validate, callbacks, resetOnSubmit, reset]
  );

  /**
   * Obtém valor de um campo
   */
  const getValue = useCallback((name: keyof T) => formData[name], [formData]);

  /**
   * Verifica se o formulário é válido
   */
  const isValid = useMemo(() => {
    return Object.keys(errors).length === 0;
  }, [errors]);

  /**
   * Verifica se o formulário foi modificado
   */
  const isDirty = useMemo(() => {
    return JSON.stringify(formData) !== JSON.stringify(initialValues);
  }, [formData, initialValues]);

  return {
    // Estado
    formData,
    errors,
    isSubmitting,
    isValid,
    isDirty,

    // Métodos de valor
    getValue,
    setValue,
    setValues,

    // Handlers para inputs
    handleChange,
    handleBlur,
    handleSubmit,

    // Validação
    validate,
    isFieldValid: (name: keyof T) =>
      isFieldValid(name, formData[name], formData),
    getFieldError: (name: keyof T) =>
      shouldShowFieldError(name) ? errors[name] : undefined,

    // Controle
    reset,
    clearErrors: clearAllErrors,
    setFieldError,
  };
}
