import { useCallback, useMemo } from "react";
import { z } from "zod";
import {
  ValidationErrors,
  ValidationResult,
  FormValidationResult,
} from "@/types/forms";
import { formatZodErrors } from "@/lib/validations";

/**
 * Hook para validação usando Zod schemas
 */
export function useValidation<T extends Record<string, unknown>>(
  schema?: z.ZodSchema<T>
) {
  /**
   * Valida um campo específico
   */
  const validateField = useCallback(
    (
      fieldName: keyof T,
      value: unknown,
      formData?: Partial<T>
    ): ValidationResult => {
      if (!schema) {
        return { isValid: true };
      }

      try {
        // Valida o objeto completo mas só retorna erro para o campo específico
        const testData = { ...formData, [fieldName]: value } as T;
        schema.parse(testData);

        return { isValid: true };
      } catch (error) {
        if (error instanceof z.ZodError) {
          const fieldError = error.errors.find(
            (err) => err.path.length > 0 && err.path[0] === fieldName
          );

          return {
            isValid: false,
            error: fieldError?.message || "Valor inválido",
          };
        }

        return {
          isValid: false,
          error: "Erro de validação",
        };
      }
    },
    [schema]
  );

  /**
   * Valida o formulário completo
   */
  const validateForm = useCallback(
    (data: Partial<T>): FormValidationResult<T> => {
      if (!schema) {
        return { isValid: true, errors: {}, data: data as T };
      }

      try {
        const validatedData = schema.parse(data);
        return {
          isValid: true,
          errors: {},
          data: validatedData,
        };
      } catch (error) {
        if (error instanceof z.ZodError) {
          const errors = formatZodErrors(error);
          return {
            isValid: false,
            errors,
          };
        }

        return {
          isValid: false,
          errors: { _form: "Erro de validação" } as ValidationErrors<T>,
        };
      }
    },
    [schema]
  );

  /**
   * Valida múltiplos campos
   */
  const validateFields = useCallback(
    (
      fields: Array<{ name: keyof T; value: unknown }>,
      formData?: Partial<T>
    ): ValidationErrors<T> => {
      const errors: ValidationErrors<T> = {};

      fields.forEach(({ name, value }) => {
        const result = validateField(name, value, formData);
        if (!result.isValid && result.error) {
          errors[name] = result.error;
        }
      });

      return errors;
    },
    [validateField]
  );

  /**
   * Verifica se um valor é válido para um campo
   */
  const isFieldValid = useCallback(
    (fieldName: keyof T, value: unknown, formData?: Partial<T>): boolean => {
      return validateField(fieldName, value, formData).isValid;
    },
    [validateField]
  );

  /**
   * Obtém a mensagem de erro para um campo
   */
  const getFieldError = useCallback(
    (
      fieldName: keyof T,
      value: unknown,
      formData?: Partial<T>
    ): string | undefined => {
      return validateField(fieldName, value, formData).error;
    },
    [validateField]
  );

  return useMemo(
    () => ({
      validateField,
      validateForm,
      validateFields,
      isFieldValid,
      getFieldError,
      hasSchema: !!schema,
    }),
    [
      validateField,
      validateForm,
      validateFields,
      isFieldValid,
      getFieldError,
      schema,
    ]
  );
}
