import React from "react";
import { BaseInput } from "./base/BaseInput";
import { formatCPF, formatCNPJ } from "@/lib/validations/rules/cpf";
import { formatPhone } from "@/lib/validations/rules/phone";
import { applyCEPMask } from "@/lib/validations/rules/cep";

interface MaskedFormFieldProps {
  label: string;
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  mask: "cpf" | "cnpj" | "cep" | "phone";
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
  className?: string;
}

/**
 * Componente MaskedFormField refatorado usando BaseInput e novas funções de máscara
 * Integrado com o sistema de validação
 */
export function MaskedFormField({
  label,
  name,
  value,
  onChange,
  onBlur,
  mask,
  placeholder,
  required = false,
  error,
  disabled = false,
  className = "",
}: MaskedFormFieldProps) {
  /**
   * Aplica a máscara baseada no tipo
   */
  const applyMask = (inputValue: string): string => {
    switch (mask) {
      case "cpf":
        return formatCPF(inputValue);
      case "cnpj":
        return formatCNPJ(inputValue);
      case "cep":
        return applyCEPMask(inputValue);
      case "phone":
        return formatPhone(inputValue);
      default:
        return inputValue;
    }
  };

  /**
   * Obtém o placeholder baseado no tipo de máscara
   */
  const getPlaceholder = (): string => {
    if (placeholder) return placeholder;

    switch (mask) {
      case "cpf":
        return "000.000.000-00";
      case "cnpj":
        return "00.000.000/0000-00";
      case "cep":
        return "00000-000";
      case "phone":
        return "(00) 00000-0000";
      default:
        return "";
    }
  };

  /**
   * Obtém o maxLength baseado no tipo de máscara
   */
  const getMaxLength = (): number | undefined => {
    switch (mask) {
      case "cpf":
        return 14; // 000.000.000-00
      case "cnpj":
        return 18; // 00.000.000/0000-00
      case "cep":
        return 9; // 00000-000
      case "phone":
        return 15; // (00) 00000-0000
      default:
        return undefined;
    }
  };

  /**
   * Manipula mudança de valor aplicando a máscara
   */
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const maskedValue = applyMask(e.target.value);

    // Cria evento sintético com valor mascarado
    const syntheticEvent = {
      ...e,
      target: {
        ...e.target,
        value: maskedValue,
      },
    };

    onChange(syntheticEvent);
  };

  /**
   * Manipula blur do campo
   */
  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    // Aplica máscara final no blur
    const finalMaskedValue = applyMask(e.target.value);

    if (finalMaskedValue !== e.target.value) {
      const syntheticChangeEvent = {
        target: {
          name,
          value: finalMaskedValue,
        },
      } as React.ChangeEvent<HTMLInputElement>;

      onChange(syntheticChangeEvent);
    }

    // Chama onBlur se fornecido
    if (onBlur) {
      const syntheticBlurEvent = {
        ...e,
        target: {
          ...e.target,
          value: finalMaskedValue,
        },
      };

      onBlur(syntheticBlurEvent);
    }
  };

  return (
    <BaseInput
      label={label}
      name={name}
      type="text"
      value={applyMask(value)}
      onChange={handleChange}
      onBlur={handleBlur}
      placeholder={getPlaceholder()}
      required={required}
      error={error}
      disabled={disabled}
      className={className}
      maxLength={getMaxLength()}
      autoComplete="off"
    />
  );
}
