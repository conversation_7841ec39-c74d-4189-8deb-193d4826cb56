import React from "react";
import { Select, SelectItem, type Selection } from "@heroui/react";

export interface Option {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface BaseSelectProps {
  label: string;
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLSelectElement>) => void;
  options: Option[];
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
  className?: string;
  selectClassName?: string;
  labelClassName?: string;
  errorClassName?: string;
  showErrorIcon?: boolean;
  multiple?: boolean;
}

/**
 * Componente base para selects com bordas finas e melhor integração com validação
 */
export function BaseSelect({
  label,
  name,
  value,
  onChange,
  onBlur,
  options,
  placeholder = "Selecione",
  required = false,
  error,
  disabled = false,
  className = "",
  selectClassName = "",
  labelClassName = "",
  errorClassName = "",
  showErrorIcon = true,
  multiple = false,
}: BaseSelectProps) {
  const hasError = !!error;

  const handleSelectionChange = (keys: Selection) => {
    const selectedValue = (Array.from(keys)[0] as string) || "";

    // Criar um evento sintético para manter compatibilidade
    const syntheticEvent = {
      target: {
        name,
        value: selectedValue,
      },
    } as React.ChangeEvent<HTMLSelectElement>;

    onChange(syntheticEvent);
  };

  const handleBlur = () => {
    if (onBlur) {
      const syntheticEvent = {
        target: {
          name,
          value,
        },
      } as React.FocusEvent<HTMLSelectElement>;

      onBlur(syntheticEvent);
    }
  };

  return (
    <div className={`flex flex-col ${className}`}>
      {/* Label */}
      <label
        htmlFor={name}
        className={`text-base font-medium text-[#4A5568] mb-1 ${labelClassName}`}
      >
        {label} {required && <span className="text-[#FF4B4B]">*</span>}
      </label>

      {/* Select */}
      <Select
        id={name}
        name={name}
        selectedKeys={value ? [value] : []}
        onSelectionChange={handleSelectionChange}
        onClose={handleBlur}
        placeholder={placeholder}
        isDisabled={disabled}
        selectionMode={multiple ? "multiple" : "single"}
        variant="bordered"
        radius="sm"
        size="lg"
        classNames={{
          trigger: [
            "bg-white",
            // Bordas mais finas (1px ao invés de 2px)
            "border",
            "border-[#E0E9F7]",
            "hover:border-[#d1d9e6]",
            "focus:border-[#4070F7]",
            "data-[open=true]:border-[#4070F7]",
            "h-[40px]",
            "min-h-[40px]",
            "rounded-md",
            // Estados de erro
            hasError ? "!border-red-500 hover:!border-red-500" : "",
            // Estado desabilitado
            disabled ? "opacity-60 cursor-not-allowed" : "",
            // Transições suaves
            "transition-colors duration-200",
            selectClassName,
          ]
            .filter(Boolean)
            .join(" "),
          value: "text-black font-normal",
          popoverContent: [
            "bg-white",
            "border",
            "border-[#E0E9F7]",
            "rounded-md",
            "shadow-lg",
          ].join(" "),
          listbox: "p-0",
        }}
      >
        {options.map((option) => (
          <SelectItem
            key={option.value}
            isDisabled={option.disabled}
            classNames={{
              base: [
                "hover:bg-[#F5F9FF]",
                "focus:bg-[#F5F9FF]",
                "data-[selected=true]:bg-[#4070F7]",
                "data-[selected=true]:text-white",
                "transition-colors duration-150",
              ].join(" "),
            }}
          >
            {option.label}
          </SelectItem>
        ))}
      </Select>

      {/* Mensagem de erro */}
      {hasError && (
        <div className={`flex items-center mt-1 ${errorClassName}`}>
          {showErrorIcon && (
            <svg
              className="w-3 h-3 text-red-500 mr-1 flex-shrink-0"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
          )}
          <span className="text-xs text-red-500">{error}</span>
        </div>
      )}
    </div>
  );
}

/**
 * Variante do BaseSelect para múltipla seleção
 */
export function BaseMultiSelect(
  props: Omit<BaseSelectProps, "multiple" | "value" | "onChange"> & {
    value: string[];
    onChange: (values: string[]) => void;
  }
) {
  const handleSelectionChange = (keys: Selection) => {
    const selectedValues = Array.from(keys) as string[];
    props.onChange(selectedValues);
  };

  return (
    <div className={`flex flex-col ${props.className}`}>
      {/* Label */}
      <label
        htmlFor={props.name}
        className={`text-base font-medium text-[#4A5568] mb-1 ${props.labelClassName}`}
      >
        {props.label}{" "}
        {props.required && <span className="text-[#FF4B4B]">*</span>}
      </label>

      {/* Select */}
      <Select
        id={props.name}
        name={props.name}
        selectedKeys={props.value}
        onSelectionChange={handleSelectionChange}
        placeholder={props.placeholder}
        isDisabled={props.disabled}
        selectionMode="multiple"
        variant="bordered"
        radius="sm"
        size="lg"
        classNames={{
          trigger: [
            "bg-white",
            "border",
            "border-[#E0E9F7]",
            "hover:border-[#d1d9e6]",
            "focus:border-[#4070F7]",
            "data-[open=true]:border-[#4070F7]",
            "min-h-[40px]",
            "rounded-md",
            props.error ? "!border-red-500 hover:!border-red-500" : "",
            props.disabled ? "opacity-60 cursor-not-allowed" : "",
            "transition-colors duration-200",
            props.selectClassName,
          ]
            .filter(Boolean)
            .join(" "),
          value: "text-black font-normal",
          popoverContent: [
            "bg-white",
            "border",
            "border-[#E0E9F7]",
            "rounded-md",
            "shadow-lg",
          ].join(" "),
        }}
      >
        {props.options.map((option) => (
          <SelectItem key={option.value} isDisabled={option.disabled}>
            {option.label}
          </SelectItem>
        ))}
      </Select>

      {/* Mensagem de erro */}
      {props.error && (
        <div className={`flex items-center mt-1 ${props.errorClassName}`}>
          {props.showErrorIcon && (
            <svg
              className="w-3 h-3 text-red-500 mr-1 flex-shrink-0"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
          )}
          <span className="text-xs text-red-500">{props.error}</span>
        </div>
      )}
    </div>
  );
}
