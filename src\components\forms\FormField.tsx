import React from "react";
import { BaseInput, BasePasswordInput } from "./base/BaseInput";

interface FormFieldProps {
  label: string;
  name: string;
  type?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
  className?: string;
  autoComplete?: string;
  maxLength?: number;
  minLength?: number;
}

/**
 * Componente FormField refatorado usando BaseInput
 * Mantém compatibilidade com a API anterior mas com melhorias visuais
 */
export function FormField({
  label,
  name,
  type = "text",
  value,
  onChange,
  onBlur,
  placeholder,
  required = false,
  error,
  disabled = false,
  className = "",
  autoComplete,
  maxLength,
  minLength,
}: FormFieldProps) {
  // Se for campo de senha, usa o BasePasswordInput com toggle de visibilidade
  if (type === "password") {
    return (
      <BasePasswordInput
        label={label}
        name={name}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        placeholder={placeholder}
        required={required}
        error={error}
        disabled={disabled}
        className={className}
        autoComplete={autoComplete}
        maxLength={maxLength}
        minLength={minLength}
      />
    );
  }

  // Para outros tipos de input, usa o BaseInput
  return (
    <BaseInput
      label={label}
      name={name}
      type={type}
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      placeholder={placeholder}
      required={required}
      error={error}
      disabled={disabled}
      className={className}
      autoComplete={autoComplete}
      maxLength={maxLength}
      minLength={minLength}
    />
  );
}
