# Sistema de Validação - SE-OUV Front

Este documento descreve o sistema de validação implementado no projeto SE-OUV Front, baseado em Zod para validação de schemas e React hooks para gerenciamento de formulários.

## Arquitetura

O sistema de validação está organizado em camadas bem definidas:

```
src/
├── lib/validations/
│   ├── schemas/          # Schemas Zod para validação
│   ├── rules/           # Regras de validação específicas
│   └── index.ts         # Exports centralizados
├── hooks/form/          # Hooks para gerenciamento de formulários
├── components/forms/    # Componentes de formulário
│   ├── base/           # Componentes base reutilizáveis
│   └── ...             # Componentes específicos
└── types/forms.ts       # Tipos TypeScript para formulários
```

## Schemas de Validação

### Schemas Principais

#### 1. Autenticação (`auth.ts`)
```typescript
import { loginUserSchema, registerUserSchema, changePasswordSchema } from '@/lib/validations/schemas/auth';

// Login
const loginData = { email: "<EMAIL>", senha: "123456" };
const result = loginUserSchema.safeParse(loginData);

// Registro
const registerData = { 
  nomeUsuario: "usuario", 
  senha: "123456", 
  confirmarSenha: "123456" 
};
```

#### 2. Dados Pessoais (`user.ts`)
```typescript
import { personalDataSchema } from '@/lib/validations/schemas/user';

const userData = {
  tipoPessoa: "fisica",
  email: "<EMAIL>",
  nomeCompleto: "João Silva",
  cpf: "123.456.789-00"
};
```

#### 3. Endereço (`address.ts`)
```typescript
import { addressSchema } from '@/lib/validations/schemas/address';

const addressData = {
  cep: "49000-000",
  cidade: "Aracaju",
  estado: "SE",
  logradouro: "Rua Principal"
};
```

#### 4. Cadastro Completo (`registration.ts`)
```typescript
import { registrationFormSchema } from '@/lib/validations/schemas/registration';

// Schema que combina todos os dados necessários para cadastro
const fullRegistrationData = {
  // Dados pessoais
  tipoPessoa: "fisica",
  email: "<EMAIL>",
  nomeCompleto: "João Silva",
  
  // Endereço (opcional)
  cep: "49000-000",
  cidade: "Aracaju",
  
  // Dados do usuário
  nomeUsuario: "joao",
  senha: "123456",
  confirmarSenha: "123456"
};
```

## Regras de Validação

### 1. CPF e CNPJ (`cpf.ts`)
```typescript
import { validateCPF, validateCNPJ, formatCPF } from '@/lib/validations/rules/cpf';

// Validação
const isValidCPF = validateCPF("123.456.789-00");

// Formatação
const formattedCPF = formatCPF("12345678900"); // "123.456.789-00"
```

### 2. Telefone (`phone.ts`)
```typescript
import { validatePhone, formatPhone } from '@/lib/validations/rules/phone';

// Validação
const isValidPhone = validatePhone("(79) 99999-9999");

// Formatação
const formattedPhone = formatPhone("79999999999"); // "(79) 99999-9999"
```

### 3. CEP (`cep.ts`)
```typescript
import { validateCEP, fetchAddressByCEP } from '@/lib/validations/rules/cep';

// Validação
const isValidCEP = validateCEP("49000-000");

// Busca de endereço
const addressInfo = await fetchAddressByCEP("49000000");
```

### 4. Email (`email.ts`)
```typescript
import { validateEmail, normalizeEmail } from '@/lib/validations/rules/email';

// Validação avançada
const emailValidation = validateEmail("<EMAIL>");
// { isValid: true, errors: [] }
```

### 5. Senha (`password.ts`)
```typescript
import { analyzePasswordStrength, validatePasswordAdvanced } from '@/lib/validations/rules/password';

// Análise de força
const analysis = analyzePasswordStrength("MinhaSenh@123");
// { strength: 'strong', score: 85, feedback: ['Senha forte!'] }

// Validação avançada
const validation = validatePasswordAdvanced("senha123", {
  minLength: 8,
  requireUppercase: true,
  requireNumbers: true
});
```

## Hooks de Formulário

### useForm

Hook principal para gerenciamento de formulários com validação integrada:

```typescript
import { useForm } from '@/hooks/form/useForm';
import { registrationFormSchema } from '@/lib/validations/schemas/registration';

function MyForm() {
  const {
    formData,
    errors,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
    setValue
  } = useForm({
    initialValues: {
      email: "",
      nomeCompleto: "",
      senha: ""
    },
    validation: {
      schema: registrationFormSchema
    },
    callbacks: {
      onSubmit: async (values) => {
        // Processar submissão
        console.log(values);
      }
    }
  });

  return (
    <form onSubmit={handleSubmit}>
      <input
        name="email"
        value={formData.email || ""}
        onChange={handleChange}
        onBlur={handleBlur}
      />
      {errors.email && <span>{errors.email}</span>}
    </form>
  );
}
```

### useValidation

Hook para validação específica com Zod:

```typescript
import { useValidation } from '@/hooks/form/useValidation';
import { z } from 'zod';

const schema = z.object({
  email: z.string().email("Email inválido")
});

function MyComponent() {
  const { validateField, validateForm } = useValidation(schema);
  
  const emailValidation = validateField("email", "<EMAIL>");
  // { isValid: true, errors: [] }
}
```

## Componentes de Formulário

### Componentes Base

#### BaseInput
```typescript
import { BaseInput } from '@/components/forms/base/BaseInput';

<BaseInput
  label="Email"
  name="email"
  type="email"
  value={value}
  onChange={handleChange}
  onBlur={handleBlur}
  error={error}
  required
/>
```

#### BaseSelect
```typescript
import { BaseSelect } from '@/components/forms/base/BaseSelect';

<BaseSelect
  label="Estado"
  name="estado"
  value={value}
  onChange={handleChange}
  options={[
    { value: "SE", label: "Sergipe" },
    { value: "BA", label: "Bahia" }
  ]}
  error={error}
/>
```

### Componentes Específicos

#### FormField
Wrapper do BaseInput com API simplificada:

```typescript
import { FormField } from '@/components/forms';

<FormField
  label="Nome"
  name="nome"
  value={formData.nome}
  onChange={handleChange}
  error={errors.nome}
  required
/>
```

#### MaskedFormField
Campo com máscara integrada:

```typescript
import { MaskedFormField } from '@/components/forms';

<MaskedFormField
  label="CPF"
  name="cpf"
  value={formData.cpf}
  onChange={handleChange}
  mask="cpf"
  error={errors.cpf}
/>
```

Máscaras disponíveis:
- `cpf`: 000.000.000-00
- `cnpj`: 00.000.000/0000-00
- `cep`: 00000-000
- `phone`: (00) 00000-0000

## Melhorias Visuais

### Bordas Mais Finas

Todos os componentes de input agora usam bordas de 1px (ao invés de 2px) para um visual mais refinado:

```css
/* Antes */
border-2 border-[#E0E9F7]

/* Depois */
border border-[#E0E9F7]
```

### Estados Visuais

- **Normal**: `border-[#E0E9F7]`
- **Hover**: `hover:border-[#d1d9e6]`
- **Focus**: `focus-within:!border-[#4070F7]`
- **Erro**: `!border-red-500`
- **Desabilitado**: `opacity-60 cursor-not-allowed`

### Transições

Todos os componentes incluem transições suaves:

```css
transition-colors duration-200
```

## Exemplo Completo

Veja a implementação completa na página de cadastro (`src/app/cadastro/page.tsx`), que demonstra:

1. ✅ Uso do hook `useForm` com schema de validação
2. ✅ Integração com componentes de formulário refatorados
3. ✅ Validação em tempo real com feedback visual
4. ✅ Máscaras automáticas para campos específicos
5. ✅ Bordas mais finas e melhor UX
6. ✅ Remoção completa de validações hardcodadas

## Benefícios

- **Type Safety**: Validação e tipos gerados automaticamente pelo Zod
- **Reutilização**: Schemas e regras podem ser reutilizados em diferentes formulários
- **Manutenibilidade**: Validações centralizadas e organizadas
- **UX Melhorada**: Feedback visual consistente e bordas mais refinadas
- **Performance**: Validação otimizada com debounce e validação condicional
- **Flexibilidade**: Sistema modular que permite customização fácil
