import { z } from 'zod';
import { validateCPF } from '../rules/cpf';
import { validatePhoneOrCell } from '../rules/phone';
import { validateEmail } from '../rules/email';

/**
 * Schema para tipo de pessoa
 */
export const personTypeSchema = z.enum(['fisica', 'juridica'], {
  errorMap: () => ({ message: 'Selecione o tipo de pessoa' }),
});

/**
 * Schema para nome completo
 */
export const fullNameSchema = z
  .string()
  .min(1, 'Nome completo é obrigatório')
  .min(2, 'Nome deve ter pelo menos 2 caracteres')
  .max(100, 'Nome deve ter no máximo 100 caracteres')
  .regex(/^[a-zA-ZÀ-ÿ\s]+$/, 'Nome deve conter apenas letras e espaços');

/**
 * Schema para data de nascimento
 */
export const birthDateSchema = z
  .string()
  .optional()
  .refine((date) => {
    if (!date) return true; // Campo opcional
    
    const birthDate = new Date(date);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    
    // Verifica se a data é válida e se a pessoa tem pelo menos 16 anos
    return !isNaN(birthDate.getTime()) && age >= 16 && birthDate <= today;
  }, 'Data de nascimento inválida ou idade menor que 16 anos');

/**
 * Schema para CPF
 */
export const cpfSchema = z
  .string()
  .optional()
  .refine((cpf) => {
    if (!cpf || cpf.trim() === '') return true; // Campo opcional
    return validateCPF(cpf);
  }, 'CPF inválido');

/**
 * Schema para RG
 */
export const rgSchema = z
  .string()
  .optional()
  .refine((rg) => {
    if (!rg || rg.trim() === '') return true; // Campo opcional
    return rg.length >= 5 && rg.length <= 20;
  }, 'RG deve ter entre 5 e 20 caracteres');

/**
 * Schema para órgão expedidor
 */
export const issuingBodySchema = z
  .string()
  .optional()
  .refine((org) => {
    if (!org || org.trim() === '') return true; // Campo opcional
    return org.length >= 2 && org.length <= 10;
  }, 'Órgão expedidor deve ter entre 2 e 10 caracteres');

/**
 * Schema para telefone
 */
export const phoneSchema = z
  .string()
  .optional()
  .refine((phone) => {
    if (!phone || phone.trim() === '') return true; // Campo opcional
    return validatePhoneOrCell(phone);
  }, 'Telefone inválido');

/**
 * Schema para gênero
 */
export const genderSchema = z
  .string()
  .optional();

/**
 * Schema para renda
 */
export const incomeSchema = z
  .string()
  .optional();

/**
 * Schema para escolaridade
 */
export const educationSchema = z
  .string()
  .optional();

/**
 * Schema completo para dados pessoais
 */
export const personalDataSchema = z.object({
  tipoPessoa: personTypeSchema,
  email: z.string().min(1, 'Email é obrigatório').refine(validateEmail, 'Email inválido'),
  nomeCompleto: fullNameSchema,
  dataNascimento: birthDateSchema,
  genero: genderSchema,
  cpf: cpfSchema,
  rg: rgSchema,
  orgaoExpedidor: issuingBodySchema,
  renda: incomeSchema,
  escolaridade: educationSchema,
  telefone: phoneSchema,
  celular: phoneSchema,
});

// Types derivados dos schemas
export type PersonType = z.infer<typeof personTypeSchema>;
export type PersonalData = z.infer<typeof personalDataSchema>;
