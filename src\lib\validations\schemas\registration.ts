import { z } from "zod";
import { personalDataSchema } from "./user";
import { addressSchema } from "./address";
import { userDataBaseSchema } from "./auth";

/**
 * Schema completo para o formulário de cadastro
 * Combina dados pessoais, endereço e dados de usuário
 */
export const registrationFormSchema = personalDataSchema
  .merge(addressSchema)
  .merge(userDataBaseSchema)
  .refine((data) => data.senha === data.confirmarSenha, {
    message: "Senhas não coincidem",
    path: ["confirmarSenha"],
  });

/**
 * Schema para validação por seções do formulário
 */
export const registrationSectionSchemas = {
  // Tipo de pessoa (obrigatório)
  personType: z.object({
    tipoPessoa: personalDataSchema.shape.tipoPessoa,
  }),

  // Dados pessoais (email e nome obrigatórios)
  personalData: z.object({
    email: personalDataSchema.shape.email,
    nomeCompleto: personalDataSchema.shape.nomeCompleto,
    dataNascimento: personalDataSchema.shape.dataNascimento,
    genero: personalDataSchema.shape.genero,
    cpf: personalDataSchema.shape.cpf,
    rg: personalDataSchema.shape.rg,
    orgaoExpedidor: personalDataSchema.shape.orgaoExpedidor,
    renda: personalDataSchema.shape.renda,
    escolaridade: personalDataSchema.shape.escolaridade,
    telefone: personalDataSchema.shape.telefone,
    celular: personalDataSchema.shape.celular,
  }),

  // Endereço (todos opcionais)
  address: addressSchema,

  // Dados do usuário (todos obrigatórios)
  userData: userDataBaseSchema,
};

// Types derivados
export type RegistrationFormData = z.infer<typeof registrationFormSchema>;
export type PersonTypeData = z.infer<
  typeof registrationSectionSchemas.personType
>;
export type PersonalDataSection = z.infer<
  typeof registrationSectionSchemas.personalData
>;
export type AddressSection = z.infer<typeof registrationSectionSchemas.address>;
export type UserDataSection = z.infer<
  typeof registrationSectionSchemas.userData
>;

/**
 * Valores iniciais para o formulário de cadastro
 */
export const initialRegistrationValues: Partial<RegistrationFormData> = {
  // Tipo de pessoa
  tipoPessoa: undefined,

  // Dados pessoais
  email: "",
  nomeCompleto: "",
  dataNascimento: "",
  genero: "",
  cpf: "",
  rg: "",
  orgaoExpedidor: "",
  renda: "",
  escolaridade: "",
  telefone: "",
  celular: "",

  // Endereço
  cep: "",
  cidade: "",
  estado: "",
  logradouro: "",
  bairro: "",
  numero: "",
  complemento: "",

  // Dados do usuário
  nomeUsuario: "",
  senha: "",
  confirmarSenha: "",
};
