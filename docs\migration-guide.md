# Guia de Migração - Sistema de Validação

Este guia ajuda na migração de formulários existentes para o novo sistema de validação baseado em Zod.

## An<PERSON> vs Depois

### ❌ Código Antigo (Hardcodado)

```typescript
// Validação hardcodada
const [errors, setErrors] = useState<Record<string, string>>({});

const validateForm = () => {
  const newErrors: Record<string, string> = {};
  
  if (!formData.email.trim()) {
    newErrors.email = "Email é obrigatório";
  } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
    newErrors.email = "Email inválido";
  }
  
  if (!formData.senha) {
    newErrors.senha = "Senha é obrigatória";
  } else if (formData.senha.length < 6) {
    newErrors.senha = "Senha deve ter pelo menos 6 caracteres";
  }
  
  setErrors(newErrors);
  return Object.keys(newErrors).length === 0;
};

const handleSubmit = (e: React.FormEvent) => {
  e.preventDefault();
  if (validateForm()) {
    // Submeter
  }
};
```

### ✅ Código Novo (Com Zod)

```typescript
import { useForm } from '@/hooks/form/useForm';
import { loginUserSchema } from '@/lib/validations/schemas/auth';

const {
  formData,
  errors,
  isSubmitting,
  handleChange,
  handleBlur,
  handleSubmit,
} = useForm({
  initialValues: { email: "", senha: "" },
  validation: { schema: loginUserSchema },
  callbacks: {
    onSubmit: async (values) => {
      // Submeter automaticamente validado
    }
  }
});
```

## Passo a Passo da Migração

### 1. Identificar o Tipo de Formulário

Determine qual schema usar baseado no formulário:

- **Login**: `loginUserSchema`
- **Cadastro**: `registrationFormSchema`
- **Dados Pessoais**: `personalDataSchema`
- **Endereço**: `addressSchema`
- **Mudança de Senha**: `changePasswordSchema`

### 2. Substituir useState por useForm

#### Antes:
```typescript
const [formData, setFormData] = useState({
  email: "",
  senha: ""
});
const [errors, setErrors] = useState<Record<string, string>>({});
const [isSubmitting, setIsSubmitting] = useState(false);
```

#### Depois:
```typescript
const {
  formData,
  errors,
  isSubmitting,
  handleChange,
  handleBlur,
  handleSubmit,
} = useForm({
  initialValues: { email: "", senha: "" },
  validation: { schema: loginUserSchema },
  callbacks: { onSubmit: async (values) => { /* ... */ } }
});
```

### 3. Atualizar Handlers

#### Antes:
```typescript
const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  const { name, value } = e.target;
  setFormData(prev => ({ ...prev, [name]: value }));
  
  // Limpar erro
  if (errors[name]) {
    setErrors(prev => ({ ...prev, [name]: "" }));
  }
};

const handleSubmit = (e: React.FormEvent) => {
  e.preventDefault();
  if (validateForm()) {
    // Submeter
  }
};
```

#### Depois:
```typescript
// Handlers fornecidos pelo useForm
// handleChange, handleBlur, handleSubmit já estão prontos
```

### 4. Atualizar Componentes de Input

#### Antes:
```typescript
<input
  name="email"
  value={formData.email}
  onChange={handleChange}
/>
{errors.email && <span>{errors.email}</span>}
```

#### Depois:
```typescript
<FormField
  label="Email"
  name="email"
  type="email"
  value={formData.email || ""}
  onChange={handleChange}
  onBlur={handleBlur}
  error={errors.email}
  required
/>
```

### 5. Migrar Campos com Máscara

#### Antes:
```typescript
// Lógica de máscara manual
const formatCPF = (value: string) => {
  return value
    .replace(/\D/g, '')
    .replace(/(\d{3})(\d)/, '$1.$2')
    .replace(/(\d{3})(\d)/, '$1.$2')
    .replace(/(\d{3})(\d{1,2})/, '$1-$2')
    .replace(/(-\d{2})\d+?$/, '$1');
};

<input
  value={formatCPF(formData.cpf)}
  onChange={(e) => {
    const formatted = formatCPF(e.target.value);
    setFormData(prev => ({ ...prev, cpf: formatted }));
  }}
/>
```

#### Depois:
```typescript
<MaskedFormField
  label="CPF"
  name="cpf"
  value={formData.cpf || ""}
  onChange={handleChange}
  onBlur={handleBlur}
  mask="cpf"
  error={errors.cpf}
/>
```

## Exemplos de Migração Completa

### Formulário de Login

#### Antes:
```typescript
function LoginForm() {
  const [formData, setFormData] = useState({ email: "", senha: "" });
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.email.trim()) {
      newErrors.email = "Email é obrigatório";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email inválido";
    }
    
    if (!formData.senha) {
      newErrors.senha = "Senha é obrigatória";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      console.log("Login:", formData);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <input
        name="email"
        value={formData.email}
        onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
      />
      {errors.email && <span>{errors.email}</span>}
      
      <input
        name="senha"
        type="password"
        value={formData.senha}
        onChange={(e) => setFormData(prev => ({ ...prev, senha: e.target.value }))}
      />
      {errors.senha && <span>{errors.senha}</span>}
      
      <button type="submit">Entrar</button>
    </form>
  );
}
```

#### Depois:
```typescript
import { useForm } from '@/hooks/form/useForm';
import { loginUserSchema } from '@/lib/validations/schemas/auth';
import { FormField } from '@/components/forms';

function LoginForm() {
  const {
    formData,
    errors,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
  } = useForm({
    initialValues: { email: "", senha: "" },
    validation: { schema: loginUserSchema },
    callbacks: {
      onSubmit: async (values) => {
        console.log("Login:", values);
      }
    }
  });
  
  return (
    <form onSubmit={handleSubmit}>
      <FormField
        label="Email"
        name="email"
        type="email"
        value={formData.email || ""}
        onChange={handleChange}
        onBlur={handleBlur}
        error={errors.email}
        required
      />
      
      <FormField
        label="Senha"
        name="senha"
        type="password"
        value={formData.senha || ""}
        onChange={handleChange}
        onBlur={handleBlur}
        error={errors.senha}
        required
      />
      
      <Button
        type="submit"
        isLoading={isSubmitting}
        disabled={isSubmitting}
      >
        {isSubmitting ? "Entrando..." : "Entrar"}
      </Button>
    </form>
  );
}
```

## Checklist de Migração

### ✅ Preparação
- [ ] Identificar todos os formulários que precisam ser migrados
- [ ] Verificar se os schemas necessários já existem
- [ ] Criar schemas customizados se necessário

### ✅ Código
- [ ] Substituir `useState` por `useForm`
- [ ] Remover funções de validação hardcodadas
- [ ] Atualizar componentes de input para usar `FormField` ou `MaskedFormField`
- [ ] Adicionar `onBlur` para validação em tempo real
- [ ] Usar `|| ""` para valores de input (evitar undefined)
- [ ] Atualizar botões de submit com `isLoading` e `disabled`

### ✅ Testes
- [ ] Testar validação em tempo real
- [ ] Testar submissão do formulário
- [ ] Verificar máscaras de formatação
- [ ] Testar estados de loading
- [ ] Validar mensagens de erro

### ✅ Limpeza
- [ ] Remover código de validação antigo
- [ ] Remover imports não utilizados
- [ ] Verificar se não há warnings no console
- [ ] Executar linter e corrigir problemas

## Problemas Comuns

### 1. Valores undefined em inputs
**Problema**: `Warning: A component is changing an uncontrolled input`

**Solução**: Sempre usar `|| ""` nos valores:
```typescript
value={formData.email || ""}
```

### 2. Validação não funciona
**Problema**: Erros não aparecem

**Solução**: Verificar se o schema está correto e se `onBlur` está sendo usado:
```typescript
onBlur={handleBlur}
```

### 3. Máscaras não aplicam
**Problema**: Formatação não acontece

**Solução**: Usar `MaskedFormField` ao invés de `FormField`:
```typescript
<MaskedFormField mask="cpf" ... />
```

### 4. TypeScript errors
**Problema**: Tipos não batem

**Solução**: Verificar se o tipo do schema bate com `initialValues`:
```typescript
const schema = z.object({
  email: z.string(),
  senha: z.string()
});

// initialValues deve ter a mesma estrutura
initialValues: { email: "", senha: "" }
```

## Recursos Adicionais

- [Documentação do Sistema de Validação](./validation-system.md)
- [Exemplos de Schemas Zod](../src/lib/validations/schemas/)
- [Componentes de Formulário](../src/components/forms/)
- [Hooks de Formulário](../src/hooks/form/)

## Suporte

Para dúvidas sobre a migração:
1. Consulte a documentação completa
2. Veja exemplos na página de cadastro (`src/app/cadastro/page.tsx`)
3. Verifique os testes unitários dos componentes
