"use client";

import { useState } from "react";
import Link from "next/link";
import { Logo } from "../components/Logo";

export default function RecuperarSenhaPage() {
  const [email, setEmail] = useState("");
  const [enviado, setEnviado] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim()) {
      setError("Email é obrigatório");
      return;
    }

    if (!/\S+@\S+\.\S+/.test(email)) {
      setError("Email inválido");
      return;
    }

    // Aqui você faria a chamada para a API de recuperação de senha
    console.log("Solicitar recuperação de senha para:", email);
    setEnviado(true);
    setError("");
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    if (error) {
      setError("");
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header com Logo */}
      <header className="bg-white shadow-sm border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-center items-center py-6">
            <Logo />
          </div>
        </div>
      </header>

      {/* Conteúdo Principal */}
      <main className="max-w-md mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Recuperar Senha
            </h1>
            <p className="text-gray-600">
              {enviado
                ? "Enviamos um email com instruções para redefinir sua senha"
                : "Digite seu email para receber instruções de recuperação"}
            </p>
          </div>

          {!enviado ? (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    error ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Digite seu email"
                />
                {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
              </div>

              <button
                type="submit"
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200"
              >
                Enviar Instruções
              </button>
            </form>
          ) : (
            <div className="text-center">
              <div className="mb-6">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    className="w-8 h-8 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
                <p className="text-gray-600 mb-4">
                  Verifique sua caixa de entrada e siga as instruções no email.
                </p>
                <p className="text-sm text-gray-500">
                  Não recebeu o email? Verifique sua pasta de spam ou{" "}
                  <button
                    onClick={() => setEnviado(false)}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    tente novamente
                  </button>
                </p>
              </div>
            </div>
          )}

          {/* Link para Login */}
          <div className="text-center mt-6">
            <Link
              href="/login"
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              ← Voltar para Login
            </Link>
          </div>
        </div>
      </main>
    </div>
  );
}
