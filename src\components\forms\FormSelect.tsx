import React from "react";
import { BaseSelect, Option } from "./base/BaseSelect";

interface FormSelectProps {
  label: string;
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLSelectElement>) => void;
  options: Option[];
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
  className?: string;
}

/**
 * Componente FormSelect refatorado usando BaseSelect
 * Mantém compatibilidade com a API anterior mas com melhorias visuais
 */
export function FormSelect({
  label,
  name,
  value,
  onChange,
  onBlur,
  options,
  placeholder = "Selecione",
  required = false,
  error,
  disabled = false,
  className = "",
}: FormSelectProps) {
  return (
    <BaseSelect
      label={label}
      name={name}
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      options={options}
      placeholder={placeholder}
      required={required}
      error={error}
      disabled={disabled}
      className={className}
    />
  );
}
