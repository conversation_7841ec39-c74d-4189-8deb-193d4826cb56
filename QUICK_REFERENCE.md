# Guia de Referência Rápida - Estrutura do Projeto

## 🚀 Comandos Rápidos

### Criar Novo Componente UI
```bash
# 1. Criar arquivo
touch src/components/ui/NovoComponente.tsx

# 2. Implementar componente
# 3. Adicionar ao index
echo "export { NovoComponente } from './NovoComponente';" >> src/components/ui/index.ts
```

### Criar Novo Hook
```bash
# 1. Criar hook
touch src/hooks/useNovaFuncionalidade.ts

# 2. Adicionar tipos se necessário
touch src/types/novaFuncionalidade.ts

# 3. Adicionar ao index
echo "export { useNovaFuncionalidade } from './useNovaFuncionalidade';" >> src/hooks/index.ts
```

### Criar Novo Service
```bash
# 1. Service
touch src/services/api/novoService.ts

# 2. Tipos
touch src/types/api/novoService.ts

# 3. Hook
touch src/hooks/api/useNovoService.ts
```

## 📁 Onde Colocar Cada Tipo de Código

| Tipo de Código | Localização | Exemplo |
|----------------|-------------|---------|
| Componente básico de UI | `components/ui/` | Button, Input, Card |
| Componente reutilizável | `components/shared/` | Logo, SearchBar |
| Componente de formulário | `components/forms/` | FormField, MaskedInput |
| Componente de layout | `components/layout/` | Header, Footer |
| Componente específico de página | `components/pages/` | CadastroForm |
| Hook para API | `hooks/api/` | useUsers, useFetch |
| Hook para formulário | `hooks/form/` | useForm, useValidation |
| Context | `contexts/` | AuthContext, ThemeContext |
| Service de API | `services/api/` | userService, authService |
| Função utilitária | `utils/` | formatDate, debounce |
| Formatador/máscara | `lib/formatters/` | applyCpfMask |
| Validação | `lib/validations/` | validateCPF |
| Tipo de API | `types/api/` | User, AuthResponse |
| Tipo de formulário | `types/forms/` | FormData, ValidationErrors |
| Constante | `constants/` | API_URLS, ROUTES |

## 🔧 Imports Mais Comuns

```typescript
// Componentes
import { Button, Input } from '@/components/ui';
import { Logo } from '@/components/shared';
import { FormField } from '@/components/forms';

// Hooks
import { useAuth } from '@/contexts/AuthContext';
import { useUsers } from '@/hooks/api';
import { useForm } from '@/hooks/form';

// Services
import { userService } from '@/services/api';

// Utils
import { formatDate } from '@/utils';
import { applyCpfMask } from '@/lib/formatters';

// Types
import type { User } from '@/types/api';
import type { FormData } from '@/types/forms';

// Constants
import { API_URLS } from '@/constants';
```

## 🎯 Padrões de Implementação

### Componente UI Básico
```typescript
interface ButtonProps {
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  onClick?: () => void;
}

export function Button({ variant = 'primary', size = 'md', children, onClick }: ButtonProps) {
  return (
    <button 
      className={`btn btn-${variant} btn-${size}`}
      onClick={onClick}
    >
      {children}
    </button>
  );
}
```

### Custom Hook
```typescript
export function useUsers() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const data = await userService.getAll();
      setUsers(data);
    } catch (err) {
      setError('Erro ao carregar usuários');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  return { users, loading, error, refetch: fetchUsers };
}
```

### Context
```typescript
interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);

  const login = async (email: string, password: string) => {
    const response = await authService.login(email, password);
    setUser(response.user);
  };

  const logout = () => setUser(null);

  return (
    <AuthContext.Provider value={{ user, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) throw new Error('useAuth must be used within AuthProvider');
  return context;
}
```

### Service
```typescript
export const userService = {
  async getAll(): Promise<User[]> {
    const response = await apiClient.get<User[]>('/users');
    return response.data;
  },

  async create(userData: CreateUserData): Promise<User> {
    const response = await apiClient.post<User>('/users', userData);
    return response.data;
  }
};
```

## ✅ Checklist para Novos Recursos

### Novo Componente
- [ ] Criado na pasta correta (`ui`, `shared`, `forms`, `layout`, `pages`)
- [ ] Props tipadas com TypeScript
- [ ] Adicionado ao barrel export (`index.ts`)
- [ ] Testes criados
- [ ] Documentação/Storybook (se aplicável)

### Novo Hook
- [ ] Nome começa com "use"
- [ ] Lógica bem encapsulada
- [ ] Tipos definidos
- [ ] Error handling implementado
- [ ] Adicionado ao barrel export
- [ ] Testes unitários

### Novo Service
- [ ] Métodos bem definidos
- [ ] Tipos de request/response criados
- [ ] Error handling consistente
- [ ] Hook correspondente criado (se necessário)
- [ ] Testes de integração

### Nova Página
- [ ] Componente principal em `components/pages/`
- [ ] Página em `app/`
- [ ] Tipos específicos definidos
- [ ] Loading e error states
- [ ] SEO metadata

## 🚨 Erros Comuns a Evitar

1. **Não usar barrel exports**: Sempre importe de `@/components/ui` ao invés de `@/components/ui/Button`
2. **Misturar responsabilidades**: Componentes UI não devem fazer chamadas de API
3. **Hooks sem "use"**: Custom hooks devem sempre começar com "use"
4. **Context muito grande**: Prefira múltiplos contexts específicos
5. **Tipos any**: Sempre defina tipos específicos
6. **Imports relativos**: Use path aliases (`@/`) ao invés de `../../../`

## 📚 Documentação Completa

Para explicações técnicas detalhadas, exemplos avançados e arquitetura completa, consulte:
- [FOLDER_STRUCTURE.md](./FOLDER_STRUCTURE.md) - Documentação técnica completa
