// Validation rules exports
export * from "./cpf";
export * from "./email";
export * from "./phone";
export * from "./cep";
export * from "./password";

// Common validation utilities
export function isRequired(value: string | undefined | null): boolean {
  return value !== undefined && value !== null && value.trim().length > 0;
}

export function hasMinLength(value: string, minLength: number): boolean {
  return value.length >= minLength;
}

export function hasMaxLength(value: string, maxLength: number): boolean {
  return value.length <= maxLength;
}

export function isNumeric(value: string): boolean {
  return /^\d+$/.test(value);
}

export function isAlphabetic(value: string): boolean {
  return /^[a-zA-ZÀ-ÿ\s]+$/.test(value);
}

export function isAlphanumeric(value: string): boolean {
  return /^[a-zA-Z0-9À-ÿ\s]+$/.test(value);
}

export function removeNonNumeric(value: string): string {
  return value.replace(/\D/g, "");
}

export function removeNonAlphabetic(value: string): string {
  return value.replace(/[^a-zA-ZÀ-ÿ\s]/g, "");
}
