/**
 * Validação de email
 */

/**
 * Regex para validação de email mais robusta
 * Baseada na RFC 5322 mas simplificada para uso prático
 */
const EMAIL_REGEX = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

/**
 * Valida formato de email
 */
export function validateEmail(email: string): boolean {
  if (!email || typeof email !== 'string') return false;
  
  // Remove espaços em branco
  const trimmedEmail = email.trim();
  
  // Verifica comprimento mínimo e máximo
  if (trimmedEmail.length < 5 || trimmedEmail.length > 254) return false;
  
  // Verifica formato básico
  if (!EMAIL_REGEX.test(trimmedEmail)) return false;
  
  // Verifica se não há pontos consecutivos
  if (trimmedEmail.includes('..')) return false;
  
  // Verifica se não começa ou termina com ponto
  const localPart = trimmedEmail.split('@')[0];
  if (localPart.startsWith('.') || localPart.endsWith('.')) return false;
  
  return true;
}

/**
 * Normaliza email (converte para lowercase e remove espaços)
 */
export function normalizeEmail(email: string): string {
  return email.trim().toLowerCase();
}

/**
 * Valida se o domínio do email é válido
 */
export function validateEmailDomain(email: string): boolean {
  if (!validateEmail(email)) return false;
  
  const domain = email.split('@')[1];
  
  // Verifica se o domínio não é muito longo
  if (domain.length > 253) return false;
  
  // Verifica se cada parte do domínio é válida
  const domainParts = domain.split('.');
  
  for (const part of domainParts) {
    // Cada parte deve ter entre 1 e 63 caracteres
    if (part.length < 1 || part.length > 63) return false;
    
    // Não pode começar ou terminar com hífen
    if (part.startsWith('-') || part.endsWith('-')) return false;
    
    // Deve conter apenas letras, números e hífens
    if (!/^[a-zA-Z0-9-]+$/.test(part)) return false;
  }
  
  return true;
}

/**
 * Extrai o domínio de um email
 */
export function extractEmailDomain(email: string): string | null {
  if (!validateEmail(email)) return null;
  return email.split('@')[1];
}

/**
 * Verifica se é um email de domínio temporário/descartável
 * Lista básica de domínios conhecidos por serem temporários
 */
export function isTemporaryEmail(email: string): boolean {
  const temporaryDomains = [
    '10minutemail.com',
    'guerrillamail.com',
    'mailinator.com',
    'tempmail.org',
    'yopmail.com',
    'temp-mail.org',
    'throwaway.email'
  ];
  
  const domain = extractEmailDomain(email);
  if (!domain) return false;
  
  return temporaryDomains.includes(domain.toLowerCase());
}
