import React from "react";

interface FormSectionProps {
  title: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

export function FormSection({
  title,
  icon,
  children,
  className = "",
}: FormSectionProps) {
  return (
    <div className={`mb-7 ${className}`}>
      <div className="flex items-center gap-2 mb-[22px]">
        {icon && <div className="w-5 h-5 text-[#1B3B6F]">{icon}</div>}
        <h3 className="text-xl font-normal text-[#1B3B6F] leading-7">
          {title}
        </h3>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">{children}</div>
    </div>
  );
}
